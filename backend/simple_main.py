"""
简化版设备管理系统后端
"""
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any
import paramiko
import time
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
import uuid

# 创建线程池执行器
executor = ThreadPoolExecutor(max_workers=10)

def test_ssh_connection(host: str, port: int, username: str, password: str = None, private_key: str = None) -> Dict[str, Any]:
    """测试SSH连接"""
    start_time = time.time()

    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # 配置连接参数
        connect_kwargs = {
            "hostname": host,
            "port": port,
            "username": username,
            "timeout": 10,
            "allow_agent": False,
            "look_for_keys": False
        }

        # 认证方式
        if password:
            connect_kwargs["password"] = password
        elif private_key:
            from io import StringIO
            key_file = StringIO(private_key)
            connect_kwargs["pkey"] = paramiko.RSAKey.from_private_key(key_file)

        # 尝试连接
        client.connect(**connect_kwargs)

        # 测试执行简单命令
        stdin, stdout, stderr = client.exec_command("echo 'connection test'")
        output = stdout.read().decode().strip()

        client.close()

        latency = round((time.time() - start_time) * 1000, 2)  # 毫秒

        return {
            "success": True,
            "message": "连接成功",
            "latency": latency,
            "test_output": output
        }

    except Exception as e:
        latency = round((time.time() - start_time) * 1000, 2)
        return {
            "success": False,
            "message": f"连接失败: {str(e)}",
            "latency": latency,
            "error": str(e)
        }

# 创建FastAPI应用
app = FastAPI(
    title="设备管理系统",
    version="1.0.0",
    description="设备管理系统 - 管理多台远端服务器的综合运维平台"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
servers_data = [
    {
        "id": 1,
        "name": "测试服务器1",
        "host": "*************",
        "port": 22,
        "username": "root",
        "auth_type": "password",
        "description": "测试用服务器",
        "tags": ["test", "development"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "生产服务器1",
        "host": "*************",
        "port": 22,
        "username": "admin",
        "auth_type": "key",
        "description": "生产环境服务器",
        "tags": ["production", "web"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

docker_images_data = [
    {
        "repository": "nginx",
        "tag": "latest",
        "image_id": "sha256:abcd1234",
        "created": "2024-01-01 00:00:00",
        "size": "133MB"
    },
    {
        "repository": "mysql",
        "tag": "8.0",
        "image_id": "sha256:efgh5678",
        "created": "2024-01-01 00:00:00",
        "size": "521MB"
    }
]

docker_containers_data = [
    {
        "container_id": "abcd1234efgh",
        "name": "web-server",
        "image": "nginx:latest",
        "status": "Up 2 hours",
        "ports": "0.0.0.0:80->80/tcp",
        "created": "2024-01-01 00:00:00"
    },
    {
        "container_id": "ijkl5678mnop",
        "name": "database",
        "image": "mysql:8.0",
        "status": "Up 1 day",
        "ports": "0.0.0.0:3306->3306/tcp",
        "created": "2024-01-01 00:00:00"
    }
]

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "设备管理系统API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "services": {
            "api": "running"
        }
    }

# 服务器管理API
@app.get("/api/v1/servers")
async def list_servers():
    """获取服务器列表"""
    return servers_data

@app.post("/api/v1/servers")
async def create_server(server_data: Dict[str, Any]):
    """创建服务器"""
    try:
        # 导入密码加密功能
        from app.core.security import encrypt_password

        # 生成新的服务器ID
        new_id = max([s["id"] for s in servers_data], default=0) + 1

        # 加密密码
        if server_data.get("password"):
            server_data["password"] = encrypt_password(server_data["password"])

        # 加密跳板机密码
        if server_data.get("jump_password"):
            server_data["jump_password"] = encrypt_password(server_data["jump_password"])

        # 创建新服务器记录
        new_server = {
            "id": new_id,
            "name": server_data.get("name"),
            "host": server_data.get("host"),
            "port": server_data.get("port", 22),
            "username": server_data.get("username"),
            "auth_type": server_data.get("auth_type", "password"),
            "password": server_data.get("password"),
            "private_key": server_data.get("private_key"),
            "private_key_path": server_data.get("private_key_path"),
            "jump_host": server_data.get("jump_host"),
            "jump_port": server_data.get("jump_port"),
            "jump_username": server_data.get("jump_username"),
            "jump_password": server_data.get("jump_password"),
            "jump_private_key": server_data.get("jump_private_key"),
            "description": server_data.get("description"),
            "tags": server_data.get("tags", []),
            "monitoring_enabled": server_data.get("monitoring_enabled", True),
            "alert_enabled": server_data.get("alert_enabled", True),
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }

        servers_data.append(new_server)
        return new_server

    except Exception as e:
        # 如果加密模块不可用，使用明文密码
        print(f"密码加密失败，使用明文存储: {e}")

        new_id = max([s["id"] for s in servers_data], default=0) + 1
        new_server = {
            "id": new_id,
            "name": server_data.get("name"),
            "host": server_data.get("host"),
            "port": server_data.get("port", 22),
            "username": server_data.get("username"),
            "auth_type": server_data.get("auth_type", "password"),
            "password": server_data.get("password"),
            "private_key": server_data.get("private_key"),
            "description": server_data.get("description"),
            "tags": server_data.get("tags", []),
            "monitoring_enabled": server_data.get("monitoring_enabled", True),
            "alert_enabled": server_data.get("alert_enabled", True),
            "is_active": True,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }

        servers_data.append(new_server)
        return new_server

@app.get("/api/v1/servers/{server_id}")
async def get_server(server_id: int):
    """获取服务器详情"""
    for server in servers_data:
        if server["id"] == server_id:
            return server
    return {"error": "服务器不存在"}

@app.put("/api/v1/servers/{server_id}")
async def update_server(server_id: int, server_data: Dict[str, Any]):
    """更新服务器"""
    # 查找要更新的服务器
    server_index = None
    for i, server in enumerate(servers_data):
        if server["id"] == server_id:
            server_index = i
            break

    if server_index is None:
        raise HTTPException(status_code=404, detail="服务器不存在")

    try:
        # 导入密码加密功能
        from app.core.security import encrypt_password

        # 加密密码
        if server_data.get("password"):
            server_data["password"] = encrypt_password(server_data["password"])

        # 更新服务器信息
        updated_server = servers_data[server_index].copy()
        for key, value in server_data.items():
            if key in updated_server or key in ["password", "private_key", "private_key_path", "jump_host", "jump_port", "jump_username", "jump_password", "jump_private_key"]:
                updated_server[key] = value

        updated_server["updated_at"] = "2024-01-01T00:00:00Z"
        servers_data[server_index] = updated_server
        return updated_server

    except Exception as e:
        # 如果加密模块不可用，使用明文密码
        print(f"密码加密失败，使用明文存储: {e}")

        updated_server = servers_data[server_index].copy()
        for key, value in server_data.items():
            if key in updated_server or key in ["password", "private_key"]:
                updated_server[key] = value

        updated_server["updated_at"] = "2024-01-01T00:00:00Z"
        servers_data[server_index] = updated_server
        return updated_server

@app.post("/api/v1/servers/{server_id}/test-connection")
async def test_connection(server_id: int):
    """测试服务器连接"""
    # 查找服务器信息
    server = None
    for s in servers_data:
        if s["id"] == server_id:
            server = s
            break

    if not server:
        raise HTTPException(status_code=404, detail="服务器不存在")

    # 解密密码
    password = server.get("password", "test123")
    try:
        from app.core.security import decrypt_password
        if password and password != "test123":
            password = decrypt_password(password)
    except Exception as e:
        print(f"密码解密失败，使用原密码: {e}")

    # 使用线程池执行SSH连接测试
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        executor,
        test_ssh_connection,
        server["host"],
        server["port"],
        server["username"],
        password,
        server.get("private_key")
    )

    return result

@app.post("/api/v1/servers/{server_id}/execute")
async def execute_command(server_id: int, request: Dict[str, Any]):
    """在服务器上执行命令"""
    return {
        "execution_id": "exec_123456",
        "exit_code": 0,
        "stdout": "命令执行成功",
        "stderr": "",
        "duration": 1,
        "started_at": "2024-01-01T00:00:00Z",
        "finished_at": "2024-01-01T00:00:01Z"
    }

# Docker管理API
@app.get("/api/v1/docker/{server_id}/images")
async def list_images(server_id: int):
    """获取Docker镜像列表"""
    return docker_images_data

@app.get("/api/v1/docker/{server_id}/containers")
async def list_containers(server_id: int):
    """获取Docker容器列表"""
    return docker_containers_data

@app.post("/api/v1/docker/{server_id}/containers")
async def create_container(server_id: int, config: Dict[str, Any]):
    """创建Docker容器"""
    # 查找服务器信息
    server = None
    for s in servers_data:
        if s["id"] == server_id:
            server = s
            break

    if not server:
        raise HTTPException(status_code=404, detail="服务器不存在")

    # 这里应该使用真实的SSH连接和Docker命令来创建容器
    # 为了演示，我们返回一个模拟的容器ID
    import uuid
    container_id = str(uuid.uuid4())[:12]

    return {
        "container_id": container_id,
        "message": f"容器 {config.get('name', 'unnamed')} 创建成功"
    }

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/start")
async def start_container(server_id: int, container_id: str):
    """启动容器"""
    return {"message": f"容器 {container_id} 启动成功"}

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/stop")
async def stop_container(server_id: int, container_id: str):
    """停止容器"""
    return {"message": f"容器 {container_id} 停止成功"}

@app.get("/api/v1/docker/{server_id}/containers/{container_id}/stats")
async def get_container_stats(server_id: int, container_id: str):
    """获取容器资源统计"""
    return {
        "container_id": container_id,
        "cpu_usage_percent": 15.5,
        "memory_usage": "256MB",
        "memory_limit": "1GB",
        "memory_usage_percent": 25.0,
        "network_io": "1.2MB / 800KB",
        "block_io": "10MB / 5MB"
    }

@app.get("/api/v1/docker/{server_id}/gpu-stats")
async def get_gpu_stats(server_id: int):
    """获取GPU统计信息"""
    return [
        {
            "gpu_id": 0,
            "name": "NVIDIA GeForce RTX 4090",
            "utilization": 75,
            "memory_used": 12000,
            "memory_total": 24000
        },
        {
            "gpu_id": 1,
            "name": "NVIDIA GeForce RTX 4090",
            "utilization": 45,
            "memory_used": 8000,
            "memory_total": 24000
        }
    ]

# 脚本管理API
@app.get("/api/v1/scripts")
async def list_scripts():
    """获取脚本列表"""
    return [
        {
            "id": 1,
            "name": "系统信息收集",
            "description": "收集服务器基本信息",
            "script_type": "shell",
            "category": "系统管理",
            "tags": ["system", "info"],
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/scripts/executions")
async def get_script_executions():
    """获取脚本执行记录"""
    return [
        {
            "id": 1,
            "script_id": 1,
            "server_id": 1,
            "status": "success",
            "output": "Linux server01 5.4.0-74-generic #83-Ubuntu",
            "error": "",
            "started_at": "2024-01-01T10:00:00Z",
            "finished_at": "2024-01-01T10:00:05Z"
        },
        {
            "id": 2,
            "script_id": 1,
            "server_id": 1,
            "status": "failed",
            "output": "",
            "error": "Permission denied",
            "started_at": "2024-01-01T11:00:00Z",
            "finished_at": "2024-01-01T11:00:02Z"
        }
    ]

# 监控API
@app.get("/api/v1/monitoring/servers")
async def get_server_monitoring():
    """获取服务器监控数据"""
    return [
        {
            "id": 1,
            "name": "测试服务器1",
            "status": "online",
            "cpu_usage": 25.5,
            "memory_usage": 68.2,
            "disk_usage": 45.8,
            "last_update": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/monitoring/servers/{server_id}/stats")
async def get_server_stats(server_id: int):
    """获取服务器详细监控数据"""
    return [
        {
            "timestamp": "2024-01-01T00:00:00Z",
            "cpu_usage": 25.5,
            "memory_usage": 68.2,
            "disk_usage": 45.8,
            "network_in": 10.5,
            "network_out": 5.2,
            "gpu_usage": 75.0,
            "gpu_memory": 60.0
        }
    ]

# 文件管理API
@app.get("/api/v1/files/{server_id}/list")
async def list_files(server_id: int, remote_path: str = "/"):
    """列出文件"""
    return {
        "path": remote_path,
        "files": [
            {
                "name": "home",
                "type": "directory",
                "size": 4096,
                "permissions": "drwxr-xr-x",
                "owner": "root",
                "group": "root",
                "modified": "2024-01-01T00:00:00Z"
            },
            {
                "name": "var",
                "type": "directory",
                "size": 4096,
                "permissions": "drwxr-xr-x",
                "owner": "root",
                "group": "root",
                "modified": "2024-01-01T00:00:00Z"
            },
            {
                "name": "test.txt",
                "type": "file",
                "size": 1024,
                "permissions": "-rw-r--r--",
                "owner": "root",
                "group": "root",
                "modified": "2024-01-01T00:00:00Z"
            }
        ]
    }

@app.post("/api/v1/files/{server_id}/upload")
async def upload_file(server_id: int):
    """上传文件"""
    return {"message": "文件上传成功", "path": "/tmp/uploaded_file.txt"}

@app.get("/api/v1/files/{server_id}/download")
async def download_file(server_id: int, remote_path: str):
    """下载文件"""
    return {"message": f"文件 {remote_path} 下载成功"}

@app.delete("/api/v1/files/{server_id}/delete")
async def delete_file(server_id: int, remote_path: str):
    """删除文件"""
    return {"message": f"文件 {remote_path} 删除成功"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
