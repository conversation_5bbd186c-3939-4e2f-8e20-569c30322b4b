"""
简化版设备管理系统后端
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any
import paramiko
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 创建线程池执行器
executor = ThreadPoolExecutor(max_workers=10)

def test_ssh_connection(host: str, port: int, username: str, password: str = None, private_key: str = None) -> Dict[str, Any]:
    """测试SSH连接"""
    start_time = time.time()

    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # 配置连接参数
        connect_kwargs = {
            "hostname": host,
            "port": port,
            "username": username,
            "timeout": 10,
            "allow_agent": False,
            "look_for_keys": False
        }

        # 认证方式
        if password:
            connect_kwargs["password"] = password
        elif private_key:
            from io import StringIO
            key_file = String<PERSON>(private_key)
            connect_kwargs["pkey"] = paramiko.RSAKey.from_private_key(key_file)

        # 尝试连接
        client.connect(**connect_kwargs)

        # 测试执行简单命令
        stdin, stdout, stderr = client.exec_command("echo 'connection test'")
        output = stdout.read().decode().strip()

        client.close()

        latency = round((time.time() - start_time) * 1000, 2)  # 毫秒

        return {
            "success": True,
            "message": "连接成功",
            "latency": latency,
            "test_output": output
        }

    except Exception as e:
        latency = round((time.time() - start_time) * 1000, 2)
        return {
            "success": False,
            "message": f"连接失败: {str(e)}",
            "latency": latency,
            "error": str(e)
        }

# 创建FastAPI应用
app = FastAPI(
    title="设备管理系统",
    version="1.0.0",
    description="设备管理系统 - 管理多台远端服务器的综合运维平台"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
servers_data = [
    {
        "id": 1,
        "name": "测试服务器1",
        "host": "*************",
        "port": 22,
        "username": "root",
        "auth_type": "password",
        "description": "测试用服务器",
        "tags": ["test", "development"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "生产服务器1",
        "host": "*************",
        "port": 22,
        "username": "admin",
        "auth_type": "key",
        "description": "生产环境服务器",
        "tags": ["production", "web"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

docker_images_data = [
    {
        "repository": "nginx",
        "tag": "latest",
        "image_id": "sha256:abcd1234",
        "created": "2024-01-01 00:00:00",
        "size": "133MB"
    },
    {
        "repository": "mysql",
        "tag": "8.0",
        "image_id": "sha256:efgh5678",
        "created": "2024-01-01 00:00:00",
        "size": "521MB"
    }
]

docker_containers_data = [
    {
        "container_id": "abcd1234efgh",
        "name": "web-server",
        "image": "nginx:latest",
        "status": "Up 2 hours",
        "ports": "0.0.0.0:80->80/tcp",
        "created": "2024-01-01 00:00:00"
    },
    {
        "container_id": "ijkl5678mnop",
        "name": "database",
        "image": "mysql:8.0",
        "status": "Up 1 day",
        "ports": "0.0.0.0:3306->3306/tcp",
        "created": "2024-01-01 00:00:00"
    }
]

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "设备管理系统API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z",
        "services": {
            "api": "running"
        }
    }

# 服务器管理API
@app.get("/api/v1/servers")
async def list_servers():
    """获取服务器列表"""
    return servers_data

@app.get("/api/v1/servers/{server_id}")
async def get_server(server_id: int):
    """获取服务器详情"""
    for server in servers_data:
        if server["id"] == server_id:
            return server
    return {"error": "服务器不存在"}

@app.post("/api/v1/servers/{server_id}/test-connection")
async def test_connection(server_id: int):
    """测试服务器连接"""
    # 查找服务器信息
    server = None
    for s in servers_data:
        if s["id"] == server_id:
            server = s
            break

    if not server:
        raise HTTPException(status_code=404, detail="服务器不存在")

    # 使用线程池执行SSH连接测试
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        executor,
        test_ssh_connection,
        server["host"],
        server["port"],
        server["username"],
        server.get("password", "test123"),  # 默认密码用于测试
        server.get("private_key")
    )

    return result

@app.post("/api/v1/servers/{server_id}/execute")
async def execute_command(server_id: int, request: Dict[str, Any]):
    """在服务器上执行命令"""
    return {
        "execution_id": "exec_123456",
        "exit_code": 0,
        "stdout": "命令执行成功",
        "stderr": "",
        "duration": 1,
        "started_at": "2024-01-01T00:00:00Z",
        "finished_at": "2024-01-01T00:00:01Z"
    }

# Docker管理API
@app.get("/api/v1/docker/{server_id}/images")
async def list_images(server_id: int):
    """获取Docker镜像列表"""
    return docker_images_data

@app.get("/api/v1/docker/{server_id}/containers")
async def list_containers(server_id: int):
    """获取Docker容器列表"""
    return docker_containers_data

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/start")
async def start_container(server_id: int, container_id: str):
    """启动容器"""
    return {"message": f"容器 {container_id} 启动成功"}

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/stop")
async def stop_container(server_id: int, container_id: str):
    """停止容器"""
    return {"message": f"容器 {container_id} 停止成功"}

@app.get("/api/v1/docker/{server_id}/containers/{container_id}/stats")
async def get_container_stats(server_id: int, container_id: str):
    """获取容器资源统计"""
    return {
        "container_id": container_id,
        "cpu_usage_percent": 15.5,
        "memory_usage": "256MB",
        "memory_limit": "1GB",
        "memory_usage_percent": 25.0,
        "network_io": "1.2MB / 800KB",
        "block_io": "10MB / 5MB"
    }

@app.get("/api/v1/docker/{server_id}/gpu-stats")
async def get_gpu_stats(server_id: int):
    """获取GPU统计信息"""
    return [
        {
            "gpu_id": 0,
            "name": "NVIDIA GeForce RTX 4090",
            "utilization": 75,
            "memory_used": 12000,
            "memory_total": 24000
        },
        {
            "gpu_id": 1,
            "name": "NVIDIA GeForce RTX 4090",
            "utilization": 45,
            "memory_used": 8000,
            "memory_total": 24000
        }
    ]

# 脚本管理API
@app.get("/api/v1/scripts")
async def list_scripts():
    """获取脚本列表"""
    return [
        {
            "id": 1,
            "name": "系统信息收集",
            "description": "收集服务器基本信息",
            "script_type": "shell",
            "category": "系统管理",
            "tags": ["system", "info"],
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/scripts/executions")
async def get_script_executions():
    """获取脚本执行记录"""
    return [
        {
            "id": 1,
            "script_id": 1,
            "server_id": 1,
            "status": "success",
            "output": "Linux server01 5.4.0-74-generic #83-Ubuntu",
            "error": "",
            "started_at": "2024-01-01T10:00:00Z",
            "finished_at": "2024-01-01T10:00:05Z"
        },
        {
            "id": 2,
            "script_id": 1,
            "server_id": 1,
            "status": "failed",
            "output": "",
            "error": "Permission denied",
            "started_at": "2024-01-01T11:00:00Z",
            "finished_at": "2024-01-01T11:00:02Z"
        }
    ]

# 监控API
@app.get("/api/v1/monitoring/servers")
async def get_server_monitoring():
    """获取服务器监控数据"""
    return [
        {
            "id": 1,
            "name": "测试服务器1",
            "status": "online",
            "cpu_usage": 25.5,
            "memory_usage": 68.2,
            "disk_usage": 45.8,
            "last_update": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/monitoring/servers/{server_id}/stats")
async def get_server_stats(server_id: int):
    """获取服务器详细监控数据"""
    return [
        {
            "timestamp": "2024-01-01T00:00:00Z",
            "cpu_usage": 25.5,
            "memory_usage": 68.2,
            "disk_usage": 45.8,
            "network_in": 10.5,
            "network_out": 5.2,
            "gpu_usage": 75.0,
            "gpu_memory": 60.0
        }
    ]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
