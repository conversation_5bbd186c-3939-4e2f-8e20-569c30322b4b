#!/usr/bin/env python3
"""
简化的后端服务器，提供基本的API端点
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime
import random

app = FastAPI(
    title="设备管理系统API",
    version="1.0.0",
    description="设备管理系统 - 管理多台远端服务器的综合运维平台"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
mock_servers = [
    {
        "id": 1,
        "name": "Web服务器01",
        "host": "*************",
        "port": 22,
        "username": "root",
        "auth_type": "password",
        "description": "主要Web服务器",
        "tags": ["web", "production"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "数据库服务器",
        "host": "*************",
        "port": 22,
        "username": "admin",
        "auth_type": "key",
        "description": "MySQL数据库服务器",
        "tags": ["database", "production"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "设备管理系统API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat() + "Z",
        "services": {
            "api": "running"
        }
    }

# 服务器管理API
@app.get("/api/v1/servers")
async def get_servers():
    """获取服务器列表"""
    return mock_servers

@app.post("/api/v1/servers")
async def create_server(server_data: dict):
    """创建服务器"""
    new_server = {
        "id": len(mock_servers) + 1,
        **server_data,
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": datetime.now().isoformat() + "Z",
        "updated_at": datetime.now().isoformat() + "Z"
    }
    mock_servers.append(new_server)
    return new_server

@app.get("/api/v1/servers/{server_id}")
async def get_server(server_id: int):
    """获取服务器详情"""
    for server in mock_servers:
        if server["id"] == server_id:
            return server
    return {"error": "服务器不存在"}

@app.put("/api/v1/servers/{server_id}")
async def update_server(server_id: int, server_data: dict):
    """更新服务器"""
    for i, server in enumerate(mock_servers):
        if server["id"] == server_id:
            mock_servers[i] = {
                **server,
                **server_data,
                "updated_at": datetime.now().isoformat() + "Z"
            }
            return mock_servers[i]
    return {"error": "服务器不存在"}

@app.delete("/api/v1/servers/{server_id}")
async def delete_server(server_id: int):
    """删除服务器"""
    global mock_servers
    mock_servers = [s for s in mock_servers if s["id"] != server_id]
    return {"message": "服务器删除成功"}

@app.post("/api/v1/servers/{server_id}/start-monitoring")
async def start_monitoring(server_id: int):
    """开始监控服务器"""
    for i, server in enumerate(mock_servers):
        if server["id"] == server_id:
            mock_servers[i]["monitoring_enabled"] = True
            return {"message": "监控已启动"}
    return {"error": "服务器不存在"}

@app.post("/api/v1/servers/{server_id}/stop-monitoring")
async def stop_monitoring(server_id: int):
    """停止监控服务器"""
    for i, server in enumerate(mock_servers):
        if server["id"] == server_id:
            mock_servers[i]["monitoring_enabled"] = False
            return {"message": "监控已停止"}
    return {"error": "服务器不存在"}

@app.post("/api/v1/servers/{server_id}/execute")
async def execute_command(server_id: int, command_data: dict):
    """在服务器上执行命令"""
    return {
        "execution_id": f"exec_{random.randint(100000, 999999)}",
        "exit_code": 0,
        "stdout": f"模拟执行命令: {command_data.get('command', '')}\\n命令执行成功",
        "stderr": "",
        "duration": round(random.uniform(0.1, 2.0), 2),
        "started_at": datetime.now().isoformat() + "Z",
        "finished_at": (datetime.now()).isoformat() + "Z"
    }

@app.post("/api/v1/servers/{server_id}/test-connection")
async def test_connection(server_id: int):
    """测试服务器连接"""
    return {
        "success": True,
        "message": "连接成功",
        "latency": random.randint(10, 100)
    }

# 脚本管理API
mock_scripts = [
    {
        "id": 1,
        "name": "系统信息收集",
        "description": "收集服务器基本系统信息",
        "content": "#!/bin/bash\nuname -a\ndf -h\nfree -m",
        "category": "系统监控",
        "tags": ["系统", "监控"],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "Docker清理",
        "description": "清理未使用的Docker镜像和容器",
        "content": "#!/bin/bash\ndocker system prune -f\ndocker image prune -f",
        "category": "Docker管理",
        "tags": ["docker", "清理"],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

@app.get("/api/v1/scripts")
async def get_scripts():
    """获取脚本列表"""
    return mock_scripts

@app.get("/api/v1/scripts/{script_id}")
async def get_script(script_id: int):
    """获取脚本详情"""
    for script in mock_scripts:
        if script["id"] == script_id:
            return script
    return {"error": "脚本不存在"}

@app.post("/api/v1/scripts")
async def create_script(script_data: dict):
    """创建脚本"""
    new_script = {
        "id": len(mock_scripts) + 1,
        **script_data,
        "created_at": datetime.now().isoformat() + "Z",
        "updated_at": datetime.now().isoformat() + "Z"
    }
    mock_scripts.append(new_script)
    return new_script

@app.put("/api/v1/scripts/{script_id}")
async def update_script(script_id: int, script_data: dict):
    """更新脚本"""
    for i, script in enumerate(mock_scripts):
        if script["id"] == script_id:
            mock_scripts[i] = {
                **script,
                **script_data,
                "updated_at": datetime.now().isoformat() + "Z"
            }
            return mock_scripts[i]
    return {"error": "脚本不存在"}

@app.delete("/api/v1/scripts/{script_id}")
async def delete_script(script_id: int):
    """删除脚本"""
    global mock_scripts
    mock_scripts = [s for s in mock_scripts if s["id"] != script_id]
    return {"message": "脚本删除成功"}

@app.post("/api/v1/scripts/{script_id}/execute")
async def execute_script(script_id: int, execution_data: dict):
    """执行脚本"""
    script = next((s for s in mock_scripts if s["id"] == script_id), None)
    if not script:
        return {"error": "脚本不存在"}

    return {
        "execution_id": f"exec_{random.randint(100000, 999999)}",
        "script_id": script_id,
        "server_id": execution_data.get("server_id"),
        "status": "success",
        "exit_code": 0,
        "output": f"执行脚本: {script['name']}\n模拟输出结果...\n执行完成",
        "error": "",
        "started_at": datetime.now().isoformat() + "Z",
        "finished_at": datetime.now().isoformat() + "Z"
    }

@app.get("/api/v1/scripts/executions")
async def get_executions():
    """获取执行记录"""
    return [
        {
            "id": 1,
            "script_id": 1,
            "server_id": 1,
            "status": "success",
            "output": "Linux server01 5.4.0-74-generic #83-Ubuntu",
            "error": "",
            "started_at": "2024-01-01T10:00:00Z",
            "finished_at": "2024-01-01T10:00:05Z"
        }
    ]

# 监控API
@app.get("/api/v1/monitoring/servers")
async def get_server_monitoring():
    """获取服务器监控状态"""
    return [
        {
            "id": 1,
            "name": "Web服务器01",
            "host": "*************",
            "status": random.choice(["online", "warning", "offline"]),
            "cpu_usage": round(random.uniform(10, 90), 2),
            "memory_usage": round(random.uniform(20, 85), 2),
            "disk_usage": round(random.uniform(30, 80), 2),
            "last_update": datetime.now().isoformat() + "Z"
        },
        {
            "id": 2,
            "name": "数据库服务器",
            "host": "*************",
            "status": "online",
            "cpu_usage": round(random.uniform(15, 75), 2),
            "memory_usage": round(random.uniform(40, 90), 2),
            "disk_usage": round(random.uniform(25, 70), 2),
            "last_update": datetime.now().isoformat() + "Z"
        }
    ]

@app.get("/api/v1/monitoring/alerts")
async def get_alerts():
    """获取告警列表"""
    return [
        {
            "id": 1,
            "server_id": 1,
            "server_name": "Web服务器01",
            "type": "cpu",
            "severity": "high",
            "message": "CPU使用率超过85%",
            "status": "active",
            "created_at": "2024-01-01T10:30:00Z",
            "acknowledged_at": None,
            "resolved_at": None
        }
    ]

# Docker API
@app.get("/api/v1/docker/{server_id}/images")
async def get_docker_images(server_id: int):
    """获取Docker镜像列表"""
    return [
        {
            "id": "sha256:abc123",
            "repository": "nginx",
            "tag": "latest",
            "size": "133MB",
            "created": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/docker/{server_id}/containers")
async def get_docker_containers(server_id: int):
    """获取Docker容器列表"""
    return [
        {
            "container_id": "container123",
            "name": "nginx-web",
            "image": "nginx:latest",
            "status": "Up 2 hours",
            "ports": "0.0.0.0:80->80/tcp",
            "created": "2024-01-01T00:00:00Z"
        },
        {
            "container_id": "container456",
            "name": "mysql-db",
            "image": "mysql:8.0",
            "status": "Up 1 day",
            "ports": "0.0.0.0:3306->3306/tcp",
            "created": "2024-01-01T00:00:00Z"
        }
    ]

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/start")
async def start_container(server_id: int, container_id: str):
    """启动容器"""
    return {
        "message": f"容器 {container_id} 启动成功",
        "container_id": container_id,
        "status": "running"
    }

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/stop")
async def stop_container(server_id: int, container_id: str):
    """停止容器"""
    return {
        "message": f"容器 {container_id} 停止成功",
        "container_id": container_id,
        "status": "stopped"
    }

@app.post("/api/v1/docker/{server_id}/containers/{container_id}/restart")
async def restart_container(server_id: int, container_id: str):
    """重启容器"""
    return {
        "message": f"容器 {container_id} 重启成功",
        "container_id": container_id,
        "status": "running"
    }

@app.delete("/api/v1/docker/{server_id}/containers/{container_id}")
async def delete_container(server_id: int, container_id: str):
    """删除容器"""
    return {
        "message": f"容器 {container_id} 删除成功",
        "container_id": container_id
    }

@app.get("/api/v1/docker/{server_id}/containers/{container_id}/logs")
async def get_container_logs(server_id: int, container_id: str, lines: int = 100):
    """获取容器日志"""
    mock_logs = []
    for i in range(min(lines, 50)):
        mock_logs.append(f"2024-01-01 12:00:{i:02d} [INFO] 容器 {container_id} 日志行 {i+1}")

    return {
        "container_id": container_id,
        "logs": "\n".join(mock_logs)
    }

@app.post("/api/v1/docker/{server_id}/images/pull")
async def pull_image(server_id: int, image_name: str):
    """拉取镜像"""
    return {
        "message": f"镜像 {image_name} 拉取成功",
        "image_name": image_name,
        "image_id": f"sha256:{random.randint(100000, 999999)}"
    }

@app.delete("/api/v1/docker/{server_id}/images/{image_id}")
async def delete_image(server_id: int, image_id: str, force: bool = False):
    """删除镜像"""
    return {
        "message": f"镜像 {image_id} 删除成功",
        "image_id": image_id,
        "force": force
    }

@app.get("/api/v1/docker/{server_id}/gpu-stats")
async def get_gpu_stats(server_id: int):
    """获取GPU统计信息"""
    return [
        {
            "id": 0,
            "name": "NVIDIA GeForce RTX 3080",
            "utilization": round(random.uniform(0, 100), 2),
            "memory_used": round(random.uniform(1000, 8000), 2),
            "memory_total": 8192,
            "temperature": round(random.uniform(40, 80), 2)
        }
    ]

# 文件管理API
@app.get("/api/v1/files/{server_id}/list")
async def list_files(server_id: int, remote_path: str = "/"):
    """列出文件和目录"""
    # 模拟文件列表
    mock_files = [
        {
            "name": "home",
            "type": "directory",
            "size": 4096,
            "modified": "2024-01-01 10:00:00",
            "permissions": "drwxr-xr-x",
            "owner": "root",
            "path": f"{remote_path}/home"
        },
        {
            "name": "var",
            "type": "directory",
            "size": 4096,
            "modified": "2024-01-01 10:00:00",
            "permissions": "drwxr-xr-x",
            "owner": "root",
            "path": f"{remote_path}/var"
        },
        {
            "name": "config.txt",
            "type": "file",
            "size": 1024,
            "modified": "2024-01-01 12:00:00",
            "permissions": "-rw-r--r--",
            "owner": "root",
            "path": f"{remote_path}/config.txt"
        },
        {
            "name": "script.sh",
            "type": "file",
            "size": 512,
            "modified": "2024-01-01 14:00:00",
            "permissions": "-rwxr-xr-x",
            "owner": "root",
            "path": f"{remote_path}/script.sh"
        }
    ]
    return mock_files

@app.post("/api/v1/files/{server_id}/upload")
async def upload_file(server_id: int):
    """上传文件"""
    return {
        "message": "文件上传成功",
        "filename": f"uploaded_file_{random.randint(1000, 9999)}.txt",
        "size": random.randint(1024, 10240)
    }

@app.get("/api/v1/files/{server_id}/download")
async def download_file(server_id: int, remote_path: str):
    """下载文件"""
    return {
        "message": "文件下载开始",
        "filename": remote_path.split("/")[-1],
        "content": f"这是文件 {remote_path} 的模拟内容"
    }

@app.delete("/api/v1/files/{server_id}/delete")
async def delete_file(server_id: int, remote_path: str):
    """删除文件或目录"""
    return {"message": f"文件/目录 {remote_path} 删除成功"}

@app.post("/api/v1/files/{server_id}/mkdir")
async def create_directory(server_id: int, remote_path: str):
    """创建目录"""
    return {"message": f"目录 {remote_path} 创建成功"}

@app.get("/api/v1/files/{server_id}/content")
async def get_file_content(server_id: int, remote_path: str):
    """获取文件内容"""
    return f"# 这是文件 {remote_path} 的模拟内容\n# 文件路径: {remote_path}\n# 服务器ID: {server_id}\n\necho 'Hello World'\ndate\nps aux | head -10"

@app.post("/api/v1/files/{server_id}/content")
async def save_file_content(server_id: int, file_data: dict):
    """保存文件内容"""
    return {
        "message": f"文件 {file_data.get('remote_path')} 保存成功",
        "size": len(file_data.get('content', ''))
    }

if __name__ == "__main__":
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
