"""
脚本管理相关API端点
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from datetime import datetime
import json

router = APIRouter()

# 数据模型
class Script(BaseModel):
    id: int
    name: str
    description: str
    content: str
    category: str
    tags: List[str] = []
    created_at: datetime
    updated_at: datetime

class ScriptCreate(BaseModel):
    name: str
    description: str
    content: str
    category: str
    tags: List[str] = []

class ScriptUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None

class ExecutionRecord(BaseModel):
    id: int
    script_id: int
    server_id: int
    status: str  # success, failed, running
    output: str
    error: str
    started_at: datetime
    finished_at: Optional[datetime] = None

class ScriptExecution(BaseModel):
    server_id: int
    parameters: dict = {}

# 模拟数据
mock_scripts = [
    {
        "id": 1,
        "name": "系统信息收集",
        "description": "收集服务器基本系统信息",
        "content": "#!/bin/bash\nuname -a\ndf -h\nfree -m",
        "category": "系统监控",
        "tags": ["系统", "监控"],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "Docker清理",
        "description": "清理未使用的Docker镜像和容器",
        "content": "#!/bin/bash\ndocker system prune -f",
        "category": "Docker",
        "tags": ["docker", "清理"],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

mock_executions = [
    {
        "id": 1,
        "script_id": 1,
        "server_id": 1,
        "status": "success",
        "output": "Linux server01 5.4.0-74-generic #83-Ubuntu",
        "error": "",
        "started_at": datetime.fromisoformat("2024-01-01T10:00:00"),
        "finished_at": datetime.fromisoformat("2024-01-01T10:00:05")
    },
    {
        "id": 2,
        "script_id": 2,
        "server_id": 1,
        "status": "failed",
        "output": "",
        "error": "Permission denied",
        "started_at": datetime.fromisoformat("2024-01-01T11:00:00"),
        "finished_at": datetime.fromisoformat("2024-01-01T11:00:02")
    }
]

@router.get("/", response_model=List[Script])
async def get_scripts():
    """获取脚本列表"""
    return mock_scripts

@router.get("/{script_id}", response_model=Script)
async def get_script(script_id: int):
    """获取脚本详情"""
    script = next((s for s in mock_scripts if s["id"] == script_id), None)
    if not script:
        raise HTTPException(status_code=404, detail="脚本不存在")
    return script

@router.post("/", response_model=Script)
async def create_script(script: ScriptCreate):
    """创建脚本"""
    new_script = {
        "id": len(mock_scripts) + 1,
        "name": script.name,
        "description": script.description,
        "content": script.content,
        "category": script.category,
        "tags": script.tags,
        "created_at": datetime.now().isoformat() + "Z",
        "updated_at": datetime.now().isoformat() + "Z"
    }
    mock_scripts.append(new_script)
    return new_script

@router.put("/{script_id}", response_model=Script)
async def update_script(script_id: int, script: ScriptUpdate):
    """更新脚本"""
    existing_script = next((s for s in mock_scripts if s["id"] == script_id), None)
    if not existing_script:
        raise HTTPException(status_code=404, detail="脚本不存在")
    
    # 更新字段
    if script.name is not None:
        existing_script["name"] = script.name
    if script.description is not None:
        existing_script["description"] = script.description
    if script.content is not None:
        existing_script["content"] = script.content
    if script.category is not None:
        existing_script["category"] = script.category
    if script.tags is not None:
        existing_script["tags"] = script.tags
    
    existing_script["updated_at"] = datetime.now().isoformat() + "Z"
    return existing_script

@router.delete("/{script_id}")
async def delete_script(script_id: int):
    """删除脚本"""
    global mock_scripts
    mock_scripts = [s for s in mock_scripts if s["id"] != script_id]
    return {"message": "脚本删除成功"}

@router.post("/{script_id}/execute", response_model=ExecutionRecord)
async def execute_script(script_id: int, execution: ScriptExecution):
    """执行脚本"""
    script = next((s for s in mock_scripts if s["id"] == script_id), None)
    if not script:
        raise HTTPException(status_code=404, detail="脚本不存在")
    
    new_execution = {
        "id": len(mock_executions) + 1,
        "script_id": script_id,
        "server_id": execution.server_id,
        "status": "success",
        "output": "脚本执行成功 (模拟输出)",
        "error": "",
        "started_at": datetime.now().isoformat() + "Z",
        "finished_at": datetime.now().isoformat() + "Z"
    }
    mock_executions.append(new_execution)
    return new_execution

@router.get("/executions", response_model=List[ExecutionRecord])
async def get_executions():
    """获取执行记录"""
    return mock_executions

@router.get("/executions/{execution_id}", response_model=ExecutionRecord)
async def get_execution(execution_id: int):
    """获取执行记录详情"""
    execution = next((e for e in mock_executions if e["id"] == execution_id), None)
    if not execution:
        raise HTTPException(status_code=404, detail="执行记录不存在")
    return execution
