"""
文件管理相关API端点
"""
from typing import List
from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

# 数据模型
class FileInfo(BaseModel):
    name: str
    path: str
    size: int
    type: str  # file, directory
    permissions: str
    modified_at: datetime

class DirectoryListing(BaseModel):
    current_path: str
    files: List[FileInfo]

# 模拟数据
def generate_mock_files(path: str = "/"):
    """生成模拟文件列表"""
    if path == "/":
        return [
            {
                "name": "home",
                "path": "/home",
                "size": 4096,
                "type": "directory",
                "permissions": "drwxr-xr-x",
                "modified_at": "2024-01-01T10:00:00Z"
            },
            {
                "name": "var",
                "path": "/var",
                "size": 4096,
                "type": "directory",
                "permissions": "drwxr-xr-x",
                "modified_at": "2024-01-01T10:00:00Z"
            },
            {
                "name": "etc",
                "path": "/etc",
                "size": 4096,
                "type": "directory",
                "permissions": "drwxr-xr-x",
                "modified_at": "2024-01-01T10:00:00Z"
            },
            {
                "name": "tmp",
                "path": "/tmp",
                "size": 4096,
                "type": "directory",
                "permissions": "drwxrwxrwt",
                "modified_at": "2024-01-01T10:00:00Z"
            }
        ]
    elif path == "/home":
        return [
            {
                "name": "user",
                "path": "/home/<USER>",
                "size": 4096,
                "type": "directory",
                "permissions": "drwxr-xr-x",
                "modified_at": "2024-01-01T10:00:00Z"
            },
            {
                "name": "admin",
                "path": "/home/<USER>",
                "size": 4096,
                "type": "directory",
                "permissions": "drwxr-xr-x",
                "modified_at": "2024-01-01T10:00:00Z"
            }
        ]
    else:
        return [
            {
                "name": "script.sh",
                "path": f"{path}/script.sh",
                "size": 1024,
                "type": "file",
                "permissions": "-rwxr-xr-x",
                "modified_at": "2024-01-01T10:00:00Z"
            },
            {
                "name": "config.txt",
                "path": f"{path}/config.txt",
                "size": 512,
                "type": "file",
                "permissions": "-rw-r--r--",
                "modified_at": "2024-01-01T10:00:00Z"
            },
            {
                "name": "data.log",
                "path": f"{path}/data.log",
                "size": 2048,
                "type": "file",
                "permissions": "-rw-r--r--",
                "modified_at": "2024-01-01T10:00:00Z"
            }
        ]

@router.post("/{server_id}/upload")
async def upload_file(server_id: int, file: UploadFile = File(...), remote_path: str = "/tmp"):
    """上传文件到服务器"""
    # 模拟文件上传
    return {
        "message": f"文件 {file.filename} 上传成功",
        "remote_path": f"{remote_path}/{file.filename}",
        "size": file.size if file.size else 0
    }

@router.get("/{server_id}/download")
async def download_file(server_id: int, remote_path: str):
    """从服务器下载文件"""
    # 模拟文件下载
    # 在实际实现中，这里应该从远程服务器获取文件
    raise HTTPException(status_code=501, detail="文件下载功能暂未实现")

@router.get("/{server_id}/list", response_model=DirectoryListing)
async def list_files(server_id: int, remote_path: str = "/"):
    """列出目录文件"""
    files = generate_mock_files(remote_path)
    return {
        "current_path": remote_path,
        "files": files
    }

@router.delete("/{server_id}/delete")
async def delete_file(server_id: int, remote_path: str):
    """删除文件"""
    return {"message": f"文件 {remote_path} 删除成功"}

@router.post("/{server_id}/mkdir")
async def create_directory(server_id: int, remote_path: str):
    """创建目录"""
    return {"message": f"目录 {remote_path} 创建成功"}

@router.get("/{server_id}/content")
async def get_file_content(server_id: int, remote_path: str):
    """获取文件内容"""
    # 模拟文件内容
    if remote_path.endswith(".sh"):
        content = "#!/bin/bash\necho 'Hello World'\ndate\n"
    elif remote_path.endswith(".txt"):
        content = "这是一个配置文件\nkey=value\ndebug=true\n"
    elif remote_path.endswith(".log"):
        content = "2024-01-01 10:00:00 INFO: 系统启动\n2024-01-01 10:01:00 INFO: 服务就绪\n"
    else:
        content = "文件内容..."
    
    return {
        "path": remote_path,
        "content": content,
        "encoding": "utf-8"
    }

@router.post("/{server_id}/content")
async def save_file_content(server_id: int, remote_path: str, content: str):
    """保存文件内容"""
    return {
        "message": f"文件 {remote_path} 保存成功",
        "size": len(content.encode('utf-8'))
    }
