"""
监控相关API端点
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from datetime import datetime
import random

router = APIRouter()

# 数据模型
class ServerStatus(BaseModel):
    id: int
    name: str
    host: str
    status: str  # online, warning, offline
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    last_update: datetime

class SystemMetrics(BaseModel):
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_in: float
    network_out: float
    gpu_usage: Optional[float] = None
    gpu_memory: Optional[float] = None

class AlertRecord(BaseModel):
    id: int
    server_id: int
    server_name: str
    type: str  # cpu, memory, disk, network
    severity: str  # low, medium, high, critical
    message: str
    status: str  # active, acknowledged, resolved
    created_at: datetime
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None

# 模拟数据
def generate_mock_server_status():
    return [
        {
            "id": 1,
            "name": "Web服务器01",
            "host": "*************",
            "status": random.choice(["online", "warning", "offline"]),
            "cpu_usage": round(random.uniform(10, 90), 2),
            "memory_usage": round(random.uniform(20, 85), 2),
            "disk_usage": round(random.uniform(30, 80), 2),
            "last_update": datetime.now()
        },
        {
            "id": 2,
            "name": "数据库服务器",
            "host": "*************",
            "status": random.choice(["online", "warning"]),
            "cpu_usage": round(random.uniform(15, 75), 2),
            "memory_usage": round(random.uniform(40, 90), 2),
            "disk_usage": round(random.uniform(25, 70), 2),
            "last_update": datetime.now()
        },
        {
            "id": 3,
            "name": "AI训练服务器",
            "host": "*************",
            "status": "online",
            "cpu_usage": round(random.uniform(60, 95), 2),
            "memory_usage": round(random.uniform(70, 95), 2),
            "disk_usage": round(random.uniform(40, 85), 2),
            "last_update": datetime.now()
        }
    ]

def generate_mock_metrics():
    return [
        {
            "timestamp": datetime.now(),
            "cpu_usage": round(random.uniform(20, 80), 2),
            "memory_usage": round(random.uniform(30, 85), 2),
            "disk_usage": round(random.uniform(25, 75), 2),
            "network_in": round(random.uniform(1, 100), 2),
            "network_out": round(random.uniform(1, 50), 2),
            "gpu_usage": round(random.uniform(0, 100), 2),
            "gpu_memory": round(random.uniform(10, 90), 2)
        }
    ]

mock_alerts = [
    {
        "id": 1,
        "server_id": 1,
        "server_name": "Web服务器01",
        "type": "cpu",
        "severity": "high",
        "message": "CPU使用率超过85%",
        "status": "active",
        "created_at": datetime.fromisoformat("2024-01-01T10:30:00"),
        "acknowledged_at": None,
        "resolved_at": None
    },
    {
        "id": 2,
        "server_id": 2,
        "server_name": "数据库服务器",
        "type": "memory",
        "severity": "medium",
        "message": "内存使用率超过80%",
        "status": "acknowledged",
        "created_at": datetime.fromisoformat("2024-01-01T09:15:00"),
        "acknowledged_at": datetime.fromisoformat("2024-01-01T09:20:00"),
        "resolved_at": None
    },
    {
        "id": 3,
        "server_id": 3,
        "server_name": "AI训练服务器",
        "type": "disk",
        "severity": "low",
        "message": "磁盘使用率超过70%",
        "status": "resolved",
        "created_at": datetime.fromisoformat("2024-01-01T08:00:00"),
        "acknowledged_at": datetime.fromisoformat("2024-01-01T08:05:00"),
        "resolved_at": datetime.fromisoformat("2024-01-01T08:30:00")
    }
]

@router.get("/servers", response_model=List[ServerStatus])
async def get_server_monitoring():
    """获取服务器监控状态"""
    return generate_mock_server_status()

@router.get("/servers/{server_id}/stats", response_model=List[SystemMetrics])
async def get_server_stats(server_id: int):
    """获取服务器详细监控数据"""
    return generate_mock_metrics()

@router.get("/servers/{server_id}/history")
async def get_history_data(server_id: int, range: str = "1h"):
    """获取历史监控数据"""
    # 生成历史数据点
    data_points = []
    for i in range(20):
        data_points.append({
            "timestamp": datetime.now(),
            "cpu_usage": round(random.uniform(20, 80), 2),
            "memory_usage": round(random.uniform(30, 85), 2),
            "disk_usage": round(random.uniform(25, 75), 2),
            "network_in": round(random.uniform(1, 100), 2),
            "network_out": round(random.uniform(1, 50), 2)
        })
    return data_points

@router.get("/alerts", response_model=List[AlertRecord])
async def get_alerts():
    """获取告警列表"""
    return mock_alerts

@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: int):
    """确认告警"""
    alert = next((a for a in mock_alerts if a["id"] == alert_id), None)
    if not alert:
        raise HTTPException(status_code=404, detail="告警不存在")
    
    alert["status"] = "acknowledged"
    alert["acknowledged_at"] = datetime.now().isoformat() + "Z"
    return {"message": "告警已确认"}

@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: int):
    """解决告警"""
    alert = next((a for a in mock_alerts if a["id"] == alert_id), None)
    if not alert:
        raise HTTPException(status_code=404, detail="告警不存在")
    
    alert["status"] = "resolved"
    alert["resolved_at"] = datetime.now().isoformat() + "Z"
    return {"message": "告警已解决"}
