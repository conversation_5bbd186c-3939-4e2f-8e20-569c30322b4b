"""
安全相关工具类
"""
import base64
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from .config import settings


class PasswordCrypto:
    """密码加密解密工具类"""
    
    def __init__(self):
        self._key = None
    
    def _get_key(self) -> bytes:
        """获取加密密钥"""
        if self._key is None:
            # 使用配置中的SECRET_KEY生成加密密钥
            password = settings.SECRET_KEY.encode()
            salt = b'device_management_salt'  # 固定盐值，生产环境应该使用随机盐值
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self._key = key
        
        return self._key
    
    def encrypt_password(self, password: str) -> str:
        """加密密码"""
        if not password:
            return ""
        
        try:
            fernet = Fernet(self._get_key())
            encrypted_password = fernet.encrypt(password.encode())
            return base64.urlsafe_b64encode(encrypted_password).decode()
        except Exception as e:
            # 如果加密失败，记录错误但不抛出异常，返回原密码
            # 这样可以保证系统的可用性
            print(f"密码加密失败: {e}")
            return password
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        if not encrypted_password:
            return ""
        
        try:
            # 尝试解密
            fernet = Fernet(self._get_key())
            encrypted_data = base64.urlsafe_b64decode(encrypted_password.encode())
            decrypted_password = fernet.decrypt(encrypted_data)
            return decrypted_password.decode()
        except Exception as e:
            # 如果解密失败，可能是明文密码，直接返回
            # 这样可以兼容旧数据
            print(f"密码解密失败，可能是明文密码: {e}")
            return encrypted_password
    
    def is_encrypted(self, password: str) -> bool:
        """检查密码是否已加密"""
        if not password:
            return False
        
        try:
            # 尝试解密，如果成功说明是加密的
            fernet = Fernet(self._get_key())
            encrypted_data = base64.urlsafe_b64decode(password.encode())
            fernet.decrypt(encrypted_data)
            return True
        except:
            # 解密失败说明是明文
            return False


# 全局密码加密实例
password_crypto = PasswordCrypto()


def encrypt_password(password: str) -> str:
    """加密密码的便捷函数"""
    return password_crypto.encrypt_password(password)


def decrypt_password(encrypted_password: str) -> str:
    """解密密码的便捷函数"""
    return password_crypto.decrypt_password(encrypted_password)


def is_password_encrypted(password: str) -> bool:
    """检查密码是否已加密的便捷函数"""
    return password_crypto.is_encrypted(password)
