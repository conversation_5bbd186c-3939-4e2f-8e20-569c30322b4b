"""
服务器相关数据模型
"""
from sqlalchemy import Column, String, Integer, Text, JSON, ForeignKey, Boolean, DateTime
from sqlalchemy.orm import relationship
from .base import Base


class Server(Base):
    """服务器模型"""
    
    name = Column(String(100), nullable=False, unique=True, comment="服务器名称")
    host = Column(String(255), nullable=False, comment="服务器地址")
    port = Column(Integer, default=22, comment="SSH端口")
    username = Column(String(100), nullable=False, comment="用户名")
    
    # SSH认证方式
    auth_type = Column(String(20), default="password", comment="认证方式: password/key")
    password = Column(Text, comment="密码(加密存储)")
    private_key = Column(Text, comment="私钥内容")
    private_key_path = Column(String(500), comment="私钥文件路径")
    
    # 跳板机配置
    jump_host = Column(String(255), comment="跳板机地址")
    jump_port = Column(Integer, comment="跳板机端口")
    jump_username = Column(String(100), comment="跳板机用户名")
    jump_password = Column(Text, comment="跳板机密码")
    jump_private_key = Column(Text, comment="跳板机私钥")
    
    # 服务器信息
    description = Column(Text, comment="服务器描述")
    tags = Column(JSON, comment="标签")
    group_id = Column(Integer, ForeignKey("servergroup.id"), comment="服务器组ID")
    
    # 监控配置
    monitoring_enabled = Column(Boolean, default=True, comment="是否启用监控")
    alert_enabled = Column(Boolean, default=True, comment="是否启用告警")
    
    # 关联关系
    group = relationship("ServerGroup", back_populates="servers")
    connections = relationship("SSHConnection", back_populates="server")
    containers = relationship("DockerContainer", back_populates="server")
    scripts = relationship("Script", back_populates="server")


class ServerGroup(Base):
    """服务器组模型"""
    
    name = Column(String(100), nullable=False, unique=True, comment="组名")
    description = Column(Text, comment="组描述")
    
    # 关联关系
    servers = relationship("Server", back_populates="group")


class SSHConnection(Base):
    """SSH连接记录模型"""
    
    server_id = Column(Integer, ForeignKey("server.id"), nullable=False)
    connection_id = Column(String(100), nullable=False, unique=True, comment="连接ID")
    status = Column(String(20), default="connecting", comment="连接状态")
    last_heartbeat = Column(DateTime, comment="最后心跳时间")
    error_message = Column(Text, comment="错误信息")
    
    # 连接统计
    connect_time = Column(DateTime, comment="连接时间")
    disconnect_time = Column(DateTime, comment="断开时间")
    bytes_sent = Column(Integer, default=0, comment="发送字节数")
    bytes_received = Column(Integer, default=0, comment="接收字节数")
    
    # 关联关系
    server = relationship("Server", back_populates="connections")
