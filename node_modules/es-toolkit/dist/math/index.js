'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const clamp = require('./clamp.js');
const inRange = require('./inRange.js');
const mean = require('./mean.js');
const meanBy = require('./meanBy.js');
const median = require('./median.js');
const medianBy = require('./medianBy.js');
const random = require('./random.js');
const randomInt = require('./randomInt.js');
const range = require('./range.js');
const rangeRight = require('./rangeRight.js');
const round = require('./round.js');
const sum = require('./sum.js');
const sumBy = require('./sumBy.js');



exports.clamp = clamp.clamp;
exports.inRange = inRange.inRange;
exports.mean = mean.mean;
exports.meanBy = meanBy.meanBy;
exports.median = median.median;
exports.medianBy = medianBy.medianBy;
exports.random = random.random;
exports.randomInt = randomInt.randomInt;
exports.range = range.range;
exports.rangeRight = rangeRight.rangeRight;
exports.round = round.round;
exports.sum = sum.sum;
exports.sumBy = sumBy.sumBy;
