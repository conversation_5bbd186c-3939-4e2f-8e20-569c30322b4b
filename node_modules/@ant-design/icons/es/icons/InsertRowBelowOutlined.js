function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import InsertRowBelowOutlinedSvg from "@ant-design/icons-svg/es/asn/InsertRowBelowOutlined";
import AntdIcon from "../components/AntdIcon";
const InsertRowBelowOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: InsertRowBelowOutlinedSvg
}));

/**![insert-row-below](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05MDQgNzY4SDEyMGMtNC40IDAtOCAzLjYtOCA4djgwYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di04MGMwLTQuNC0zLjYtOC04LTh6bS0yNS4zLTYwOEgxNDUuM2MtMTguNCAwLTMzLjMgMTQuMy0zMy4zIDMydjQ2NGMwIDE3LjcgMTQuOSAzMiAzMy4zIDMyaDczMy4zYzE4LjQgMCAzMy4zLTE0LjMgMzMuMy0zMlYxOTJjLjEtMTcuNy0xNC44LTMyLTMzLjItMzJ6TTM2MCA2MTZIMTg0VjQ1NmgxNzZ2MTYwem0wLTIyNEgxODRWMjMyaDE3NnYxNjB6bTI0MCAyMjRINDI0VjQ1NmgxNzZ2MTYwem0wLTIyNEg0MjRWMjMyaDE3NnYxNjB6bTI0MCAyMjRINjY0VjQ1NmgxNzZ2MTYwem0wLTIyNEg2NjRWMjMyaDE3NnYxNjB6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(InsertRowBelowOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InsertRowBelowOutlined';
}
export default RefIcon;