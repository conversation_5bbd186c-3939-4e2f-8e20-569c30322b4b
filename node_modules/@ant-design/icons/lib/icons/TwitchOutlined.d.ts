import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![twitch](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTY2LjEzIDExMkwxMTQgMjUxLjE3djU1Ni40NmgxOTEuMlY5MTJoMTA0LjRsMTA0LjIzLTEwNC40aDE1Ni41TDg3OSA1OTlWMTEyem02OS41NCA2OS41SDgwOS41djM4Mi42M0w2ODcuNzcgNjg1Ljg3SDQ5Ni41TDM5Mi4yNyA3OTAuMVY2ODUuODdoLTE1Ni42ek00MjcgNTI5LjRoNjkuNVYzMjAuNzNINDI3em0xOTEuMTcgMGg2OS41M1YzMjAuNzNoLTY5LjUzeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
