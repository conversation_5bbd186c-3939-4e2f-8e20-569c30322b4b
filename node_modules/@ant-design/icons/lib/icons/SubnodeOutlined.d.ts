import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![subnode](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02ODggMjQwYy0xMzggMC0yNTIgMTAyLjgtMjY5LjYgMjM2SDI0OWE5NS45MiA5NS45MiAwIDAwLTg5LTYwYy01MyAwLTk2IDQzLTk2IDk2czQzIDk2IDk2IDk2YzQwLjMgMCA3NC44LTI0LjggODktNjBoMTY5LjNDNDM2IDY4MS4yIDU1MCA3ODQgNjg4IDc4NGMxNTAuMiAwIDI3Mi0xMjEuOCAyNzItMjcyUzgzOC4yIDI0MCA2ODggMjQwem0xMjggMjk4YzAgNC40LTMuNiA4LTggOGgtODZ2ODZjMCA0LjQtMy42IDgtOCA4aC01MmMtNC40IDAtOC0zLjYtOC04di04NmgtODZjLTQuNCAwLTgtMy42LTgtOHYtNTJjMC00LjQgMy42LTggOC04aDg2di04NmMwLTQuNCAzLjYtOCA4LThoNTJjNC40IDAgOCAzLjYgOCA4djg2aDg2YzQuNCAwIDggMy42IDggOHY1MnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
