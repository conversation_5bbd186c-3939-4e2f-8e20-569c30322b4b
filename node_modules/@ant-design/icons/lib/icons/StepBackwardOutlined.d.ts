import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![step-backward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0Ny42IDUyOC45NWwzODMuMiAzMDEuMDJjMTQuMjUgMTEuMiAzNS4yIDEuMSAzNS4yLTE2Ljk1VjIxMC45N2MwLTE4LjA1LTIwLjk1LTI4LjE0LTM1LjItMTYuOTRMMzQ3LjYgNDk1LjA1YTIxLjUzIDIxLjUzIDAgMDAwIDMzLjlNMzMwIDg2NGgtNjRhOCA4IDAgMDEtOC04VjE2OGE4IDggMCAwMTgtOGg2NGE4IDggMCAwMTggOHY2ODhhOCA4IDAgMDEtOCA4IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
