import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![select](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoMzYwYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDE4NFYxODRoNjU2djMyMGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjUzLjMgNTk5LjRsNTIuMi01Mi4yYTguMDEgOC4wMSAwIDAwLTQuNy0xMy42bC0xNzkuNC0yMWMtNS4xLS42LTkuNSAzLjctOC45IDguOWwyMSAxNzkuNGMuOCA2LjYgOC45IDkuNCAxMy42IDQuN2w1Mi40LTUyLjQgMjU2LjIgMjU2LjJjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGw0Mi40LTQyLjRjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM0w2NTMuMyA1OTkuNHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
