import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![shopping-cart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyMi45IDcwMS45SDMyNy40bDI5LjktNjAuOSA0OTYuOC0uOWMxNi44IDAgMzEuMi0xMiAzNC4yLTI4LjZsNjguOC0zODUuMWMxLjgtMTAuMS0uOS0yMC41LTcuNS0yOC40YTM0Ljk5IDM0Ljk5IDAgMDAtMjYuNi0xMi41bC02MzItMi4xLTUuNC0yNS40Yy0zLjQtMTYuMi0xOC0yOC0zNC42LTI4SDk2LjVhMzUuMyAzNS4zIDAgMTAwIDcwLjZoMTI1LjlMMjQ2IDMxMi44bDU4LjEgMjgxLjMtNzQuOCAxMjIuMWEzNC45NiAzNC45NiAwIDAwLTMgMzYuOGM2IDExLjkgMTguMSAxOS40IDMxLjUgMTkuNGg2Mi44YTEwMi40MyAxMDIuNDMgMCAwMC0yMC42IDYxLjdjMCA1Ni42IDQ2IDEwMi42IDEwMi42IDEwMi42czEwMi42LTQ2IDEwMi42LTEwMi42YzAtMjIuMy03LjQtNDQtMjAuNi02MS43aDE2MS4xYTEwMi40MyAxMDIuNDMgMCAwMC0yMC42IDYxLjdjMCA1Ni42IDQ2IDEwMi42IDEwMi42IDEwMi42czEwMi42LTQ2IDEwMi42LTEwMi42YzAtMjIuMy03LjQtNDQtMjAuNi02MS43SDkyM2MxOS40IDAgMzUuMy0xNS44IDM1LjMtMzUuM2EzNS40MiAzNS40MiAwIDAwLTM1LjQtMzUuMnpNMzA1LjcgMjUzbDU3NS44IDEuOS01Ni40IDMxNS44LTQ1Mi4zLjhMMzA1LjcgMjUzem05Ni45IDYxMi43Yy0xNy40IDAtMzEuNi0xNC4yLTMxLjYtMzEuNiAwLTE3LjQgMTQuMi0zMS42IDMxLjYtMzEuNnMzMS42IDE0LjIgMzEuNiAzMS42YTMxLjYgMzEuNiAwIDAxLTMxLjYgMzEuNnptMzI1LjEgMGMtMTcuNCAwLTMxLjYtMTQuMi0zMS42LTMxLjYgMC0xNy40IDE0LjItMzEuNiAzMS42LTMxLjZzMzEuNiAxNC4yIDMxLjYgMzEuNmEzMS42IDMxLjYgMCAwMS0zMS42IDMxLjZ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
