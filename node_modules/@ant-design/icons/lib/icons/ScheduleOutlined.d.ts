import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![schedule](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAyMjRINzY4di01NmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZINTQ4di01NmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZIMzI4di01NmMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU3NmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjU2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNTY4SDEzNlYyOTZoMTIwdjU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTU2aDE0OHY1NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di01NmgxNDh2NTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNTZoMTIwdjQ5NnpNNDE2IDQ5NkgyMzJjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04em0wIDEzNkgyMzJjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04em0zMDguMi0xNzcuNEw2MjAuNiA1OTguM2wtNTIuOC03My4xYy0zLTQuMi03LjgtNi42LTEyLjktNi42SDUwMGMtNi41IDAtMTAuMyA3LjQtNi41IDEyLjdsMTE0LjEgMTU4LjJhMTUuOSAxNS45IDAgMDAyNS44IDBsMTY1LTIyOC43YzMuOC01LjMgMC0xMi43LTYuNS0xMi43SDczN2MtNS0uMS05LjggMi40LTEyLjggNi41eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
