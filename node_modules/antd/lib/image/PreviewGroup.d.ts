import * as React from 'react';
import type { GroupConsumerProps } from 'rc-image/lib/PreviewGroup';
export declare const icons: {
    rotateLeft: React.JSX.Element;
    rotateRight: React.JSX.Element;
    zoomIn: React.JSX.Element;
    zoomOut: React.JSX.Element;
    close: React.JSX.Element;
    left: React.JSX.Element;
    right: React.JSX.Element;
    flipX: React.JSX.Element;
    flipY: React.JSX.Element;
};
declare const InternalPreviewGroup: React.FC<GroupConsumerProps>;
export default InternalPreviewGroup;
