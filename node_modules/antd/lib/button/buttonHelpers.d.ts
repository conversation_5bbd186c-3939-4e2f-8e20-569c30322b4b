import React from 'react';
import type { BaseButtonProps, LegacyButtonType } from './button';
export declare const isTwoCNChar: (string: string) => boolean;
export declare function convertLegacyProps(type?: LegacyButtonType): Pick<BaseButtonProps, 'danger' | 'type'>;
export declare function isString(str: unknown): str is string;
export declare function isUnBorderedButtonVariant(type?: ButtonVariantType): type is "text" | "link";
export declare function spaceChildren(children: React.ReactNode, needInserted: boolean): (number | React.JSX.Element)[] | null | undefined;
declare const _ButtonTypes: readonly ["default", "primary", "dashed", "link", "text"];
export type ButtonType = (typeof _ButtonTypes)[number];
declare const _ButtonShapes: readonly ["default", "circle", "round"];
export type ButtonShape = (typeof _ButtonShapes)[number];
declare const _ButtonHTMLTypes: readonly ["submit", "button", "reset"];
export type ButtonHTMLType = (typeof _ButtonHTMLTypes)[number];
export declare const _ButtonVariantTypes: readonly ["outlined", "dashed", "solid", "filled", "text", "link"];
export type ButtonVariantType = (typeof _ButtonVariantTypes)[number];
export declare const _ButtonColorTypes: readonly ["default", "primary", "danger", "blue", "purple", "cyan", "green", "magenta", "pink", "red", "orange", "yellow", "volcano", "geekblue", "lime", "gold"];
export type ButtonColorType = (typeof _ButtonColorTypes)[number];
export {};
