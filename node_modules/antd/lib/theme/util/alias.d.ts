import type { AliasToken, MapToken, OverrideToken } from '../interface';
/** Raw merge of `@ant-design/cssinjs` token. Which need additional process */
type RawMergedToken = MapToken & OverrideToken & {
    override: Partial<AliasToken>;
};
/**
 * Seed (designer) > Derivative (designer) > <PERSON><PERSON> (developer).
 *
 * Merge seed & derivative & override token and generate alias token for developer.
 */
export default function formatToken(derivativeToken: RawMergedToken): AliasToken;
export {};
