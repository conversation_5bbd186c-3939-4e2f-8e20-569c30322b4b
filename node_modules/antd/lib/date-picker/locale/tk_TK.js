"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _tk_TK = _interopRequireDefault(require("rc-picker/lib/locale/tk_TK"));
var _tk_TK2 = _interopRequireDefault(require("../../time-picker/locale/tk_TK"));
const locale = {
  lang: Object.assign({
    placeholder: 'Wagt saýlaň',
    rangePlaceholder: ['<PERSON><PERSON><PERSON>ýan wagty', '<PERSON><PERSON>r<PERSON><PERSON> wagty'],
    yearPlaceholder: 'Ýyl saýlaň',
    quarterPlaceholder: 'Ç<PERSON>r<PERSON>ek saýlaň',
    monthPlaceholder: 'Aý saýlaň',
    weekPlaceholder: 'Hep<PERSON> saýlaň',
    rangeYearPlaceholder: ['<PERSON>şlanýan ýyly', '<PERSON><PERSON><PERSON><PERSON><PERSON> ýyly'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> ç<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    rangeMonthPlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>'],
    rangeWeekPlaceholder: ['Başlanýan hepdesi', 'Gutarýan hepdesi']
  }, _tk_TK.default),
  timePickerLocale: Object.assign({}, _tk_TK2.default)
};
var _default = exports.default = locale;