import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import NodeExpandOutlinedSvg from "@ant-design/icons-svg/es/asn/NodeExpandOutlined";
import AntdIcon from "../components/AntdIcon";
var NodeExpandOutlined = function NodeExpandOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: NodeExpandOutlinedSvg
  }));
};

/**![node-expand](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NTIgNjEyYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDI5OGE5NS45MiA5NS45MiAwIDAwLTg5LTYwYy01MyAwLTk2IDQzLTk2IDk2czQzIDk2IDk2IDk2YzQwLjMgMCA3NC44LTI0LjggODktNjBoMTUwLjN2MTUyYzAgNTUuMiA0NC44IDEwMCAxMDAgMTAwSDk1MmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOEg1NDguM2MtMTUuNSAwLTI4LTEyLjUtMjgtMjhWNjEySDk1MnpNNDU2IDM0NGgyNjR2OTguMmMwIDguMSA5LjUgMTIuOCAxNS44IDcuN2wxNzIuNS0xMzYuMmM1LTMuOSA1LTExLjQgMC0xNS4zTDczNS44IDE2Mi4xYy02LjQtNS4xLTE1LjgtLjUtMTUuOCA3LjdWMjY4SDQ1NmMtNC40IDAtOCAzLjYtOCA4djYwYzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(NodeExpandOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NodeExpandOutlined';
}
export default RefIcon;