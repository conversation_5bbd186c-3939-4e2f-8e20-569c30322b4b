import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import NotificationOutlinedSvg from "@ant-design/icons-svg/es/asn/NotificationOutlined";
import AntdIcon from "../components/AntdIcon";
var NotificationOutlined = function NotificationOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: NotificationOutlinedSvg
  }));
};

/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJjLTMuOCAwLTcuNy43LTExLjYgMi4zTDI5MiAzNDUuOUgxMjhjLTguOCAwLTE2IDcuNC0xNiAxNi42djI5OWMwIDkuMiA3LjIgMTYuNiAxNiAxNi42aDEwMS43Yy0zLjcgMTEuNi01LjcgMjMuOS01LjcgMzYuNCAwIDY1LjkgNTMuOCAxMTkuNSAxMjAgMTE5LjUgNTUuNCAwIDEwMi4xLTM3LjYgMTE1LjktODguNGw0MDguNiAxNjQuMmMzLjkgMS41IDcuOCAyLjMgMTEuNiAyLjMgMTYuOSAwIDMyLTE0LjIgMzItMzMuMlYxNDUuMkM5MTIgMTI2LjIgODk3IDExMiA4ODAgMTEyek0zNDQgNzYyLjNjLTI2LjUgMC00OC0yMS40LTQ4LTQ3LjggMC0xMS4yIDMuOS0yMS45IDExLTMwLjRsODQuOSAzNC4xYy0yIDI0LjYtMjIuNyA0NC4xLTQ3LjkgNDQuMXptNDk2IDU4LjRMMzE4LjggNjExLjNsLTEyLjktNS4ySDE4NFY0MTcuOWgxMjEuOWwxMi45LTUuMkw4NDAgMjAzLjN2NjE3LjR6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(NotificationOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NotificationOutlined';
}
export default RefIcon;