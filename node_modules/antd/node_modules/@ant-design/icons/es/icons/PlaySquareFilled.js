import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PlaySquareFilledSvg from "@ant-design/icons-svg/es/asn/PlaySquareFilled";
import AntdIcon from "../components/AntdIcon";
var PlaySquareFilled = function PlaySquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PlaySquareFilledSvg
  }));
};

/**![play-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjQxLjcgNTIwLjhMNDQyLjMgNjc3LjZjLTcuNCA1LjgtMTguMy42LTE4LjMtOC44VjM1NS4zYzAtOS40IDEwLjktMTQuNyAxOC4zLTguOGwxOTkuNCAxNTYuN2ExMS4yIDExLjIgMCAwMTAgMTcuNnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PlaySquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PlaySquareFilled';
}
export default RefIcon;