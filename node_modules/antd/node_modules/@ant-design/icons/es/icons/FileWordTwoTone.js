import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileWordTwoToneSvg from "@ant-design/icons-svg/es/asn/FileWordTwoTone";
import AntdIcon from "../components/AntdIcon";
var FileWordTwoTone = function FileWordTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FileWordTwoToneSvg
  }));
};

/**![file-word](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptMTAxLjMgMTI5LjNjMS4zLTUuNCA2LjEtOS4zIDExLjctOS4zaDM1LjZhMTIuMDQgMTIuMDQgMCAwMTExLjYgMTUuMWwtNzQuNCAyNzZjLTEuNCA1LjMtNi4yIDguOS0xMS42IDguOWgtMzEuOGMtNS40IDAtMTAuMi0zLjctMTEuNi04LjlsLTUyLjgtMTk3LTUyLjggMTk3Yy0xLjQgNS4zLTYuMiA4LjktMTEuNiA4LjloLTMyYy01LjQgMC0xMC4yLTMuNy0xMS42LTguOWwtNzQuMi0yNzZhMTIuMDIgMTIuMDIgMCAwMTExLjYtMTUuMWgzNS40YzUuNiAwIDEwLjQgMy45IDExLjcgOS4zTDQzNC42IDY4MGw0OS43LTE5OC45YzEuMy01LjQgNi4xLTkuMSAxMS42LTkuMWgzMi4yYzUuNSAwIDEwLjMgMy43IDExLjYgOS4xbDQ5LjggMTk5LjMgNDUuOC0xOTkuMXoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTYwMiAxMzcuOEw3OTAuMiAzMjZINjAyVjEzNy44ek03OTIgODg4SDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUyOC4xIDQ3MmgtMzIuMmMtNS41IDAtMTAuMyAzLjctMTEuNiA5LjFMNDM0LjYgNjgwbC00Ni4xLTE5OC43Yy0xLjMtNS40LTYuMS05LjMtMTEuNy05LjNoLTM1LjRhMTIuMDIgMTIuMDIgMCAwMC0xMS42IDE1LjFsNzQuMiAyNzZjMS40IDUuMiA2LjIgOC45IDExLjYgOC45aDMyYzUuNCAwIDEwLjItMy42IDExLjYtOC45bDUyLjgtMTk3IDUyLjggMTk3YzEuNCA1LjIgNi4yIDguOSAxMS42IDguOWgzMS44YzUuNCAwIDEwLjItMy42IDExLjYtOC45bDc0LjQtMjc2YTEyLjA0IDEyLjA0IDAgMDAtMTEuNi0xNS4xSDY0N2MtNS42IDAtMTAuNCAzLjktMTEuNyA5LjNsLTQ1LjggMTk5LjEtNDkuOC0xOTkuM2MtMS4zLTUuNC02LjEtOS4xLTExLjYtOS4xeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileWordTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileWordTwoTone';
}
export default RefIcon;