import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ProjectFilledSvg from "@ant-design/icons-svg/es/asn/ProjectFilled";
import AntdIcon from "../components/AntdIcon";
var ProjectFilled = function ProjectFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ProjectFilledSvg
  }));
};

/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNMzY4IDc0NGMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2NDY0em0xOTItMjgwYzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOFYyODBjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHYxODR6bTE5MiA3MmMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2MjU2eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ProjectFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ProjectFilled';
}
export default RefIcon;