import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PoweroffOutlinedSvg from "@ant-design/icons-svg/es/asn/PoweroffOutlined";
import AntdIcon from "../components/AntdIcon";
var PoweroffOutlined = function PoweroffOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PoweroffOutlinedSvg
  }));
};

/**![poweroff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNS42IDEyNC45YTggOCAwIDAwLTExLjYgNy4ydjY0LjJjMCA1LjUgMi45IDEwLjYgNy41IDEzLjZhMzUyLjIgMzUyLjIgMCAwMTYyLjIgNDkuOGMzMi43IDMyLjggNTguNCA3MC45IDc2LjMgMTEzLjNhMzU1IDM1NSAwIDAxMjcuOSAxMzguN2MwIDQ4LjEtOS40IDk0LjgtMjcuOSAxMzguN2EzNTUuOTIgMzU1LjkyIDAgMDEtNzYuMyAxMTMuMyAzNTMuMDYgMzUzLjA2IDAgMDEtMTEzLjIgNzYuNGMtNDMuOCAxOC42LTkwLjUgMjgtMTM4LjUgMjhzLTk0LjctOS40LTEzOC41LTI4YTM1My4wNiAzNTMuMDYgMCAwMS0xMTMuMi03Ni40QTM1NS45MiAzNTUuOTIgMCAwMTE4NCA2NTAuNGEzNTUgMzU1IDAgMDEtMjcuOS0xMzguN2MwLTQ4LjEgOS40LTk0LjggMjcuOS0xMzguNyAxNy45LTQyLjQgNDMuNi04MC41IDc2LjMtMTEzLjMgMTktMTkgMzkuOC0zNS42IDYyLjItNDkuOCA0LjctMi45IDcuNS04LjEgNy41LTEzLjZWMTMyYzAtNi02LjMtOS44LTExLjYtNy4yQzE3OC41IDE5NS4yIDgyIDMzOS4zIDgwIDUwNi4zIDc3LjIgNzQ1LjEgMjcyLjUgOTQzLjUgNTExLjIgOTQ0YzIzOSAuNSA0MzIuOC0xOTMuMyA0MzIuOC00MzIuNCAwLTE2OS4yLTk3LTMxNS43LTIzOC40LTM4Ni43ek00ODAgNTYwaDY0YzQuNCAwIDgtMy42IDgtOFY4OGMwLTQuNC0zLjYtOC04LThoLTY0Yy00LjQgMC04IDMuNi04IDh2NDY0YzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PoweroffOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PoweroffOutlined';
}
export default RefIcon;