import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UsbTwoToneSvg from "@ant-design/icons-svg/es/asn/UsbTwoTone";
import AntdIcon from "../components/AntdIcon";
var UsbTwoTone = function UsbTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: UsbTwoToneSvg
  }));
};

/**![usb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc1OS45IDUwNEgyNjQuMWMtMjYuNSAwLTQ4LjEgMTkuNy00OC4xIDQ0djI5Mmg1OTJWNTQ4YzAtMjQuMy0yMS42LTQ0LTQ4LjEtNDR6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik00NTYgMjQ4aC00OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptMTYwIDBoLTQ4Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNzYwIDQzMlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMjk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyODhjLTY2LjIgMC0xMjAgNTIuMS0xMjAgMTE2djM1NmMwIDQuNCAzLjYgOCA4IDhoNzIwYzQuNCAwIDgtMy42IDgtOFY1NDhjMC02My45LTUzLjgtMTE2LTEyMC0xMTZ6TTMzNiAxODRoMzUydjI0OEgzMzZWMTg0em00NzIgNjU2SDIxNlY1NDhjMC0yNC4zIDIxLjYtNDQgNDguMS00NGg0OTUuOGMyNi41IDAgNDguMSAxOS43IDQ4LjEgNDR2MjkyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(UsbTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UsbTwoTone';
}
export default RefIcon;