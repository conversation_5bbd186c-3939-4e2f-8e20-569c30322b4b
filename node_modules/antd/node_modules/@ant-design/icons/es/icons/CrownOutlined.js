import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CrownOutlinedSvg from "@ant-design/icons-svg/es/asn/CrownOutlined";
import AntdIcon from "../components/AntdIcon";
var CrownOutlined = function CrownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CrownOutlinedSvg
  }));
};

/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5OS42IDI3Ni41TDcwNSAzOTYuNCA1MTguNCAxNDcuNWE4LjA2IDguMDYgMCAwMC0xMi45IDBMMzE5IDM5Ni40IDEyNC4zIDI3Ni41Yy01LjctMy41LTEzLjEgMS4yLTEyLjIgNy45TDE4OC41IDg2NWMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuOC02LjctNi41LTExLjQtMTIuMy03Ljl6bS0xMjYgNTM0LjFIMjUwLjNsLTUzLjgtNDA5LjQgMTM5LjggODYuMUw1MTIgMjUyLjlsMTc1LjcgMjM0LjQgMTM5LjgtODYuMS01My45IDQwOS40ek01MTIgNTA5Yy02Mi4xIDAtMTEyLjYgNTAuNS0xMTIuNiAxMTIuNlM0NDkuOSA3MzQuMiA1MTIgNzM0LjJzMTEyLjYtNTAuNSAxMTIuNi0xMTIuNlM1NzQuMSA1MDkgNTEyIDUwOXptMCAxNjAuOWMtMjYuNiAwLTQ4LjItMjEuNi00OC4yLTQ4LjMgMC0yNi42IDIxLjYtNDguMyA0OC4yLTQ4LjNzNDguMiAyMS42IDQ4LjIgNDguM2MwIDI2LjYtMjEuNiA0OC4zLTQ4LjIgNDguM3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CrownOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CrownOutlined';
}
export default RefIcon;