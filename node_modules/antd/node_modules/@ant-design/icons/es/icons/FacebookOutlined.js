import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FacebookOutlinedSvg from "@ant-design/icons-svg/es/asn/FacebookOutlined";
import AntdIcon from "../components/AntdIcon";
var FacebookOutlined = function FacebookOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FacebookOutlinedSvg
  }));
};

/**![facebook](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTMyIDczNkg2NjMuOVY2MDIuMmgxMDRsMTUuNi0xMjAuN0g2NjMuOXYtNzcuMWMwLTM1IDkuNy01OC44IDU5LjgtNTguOGg2My45di0xMDhjLTExLjEtMS41LTQ5LTQuOC05My4yLTQuOC05Mi4yIDAtMTU1LjMgNTYuMy0xNTUuMyAxNTkuNnY4OUg0MzQuOXYxMjAuN2gxMDQuM1Y4NDhIMTc2VjE3Nmg2NzJ2NjcyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FacebookOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FacebookOutlined';
}
export default RefIcon;