import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CompassFilledSvg from "@ant-design/icons-svg/es/asn/CompassFilled";
import AntdIcon from "../components/AntdIcon";
var CompassFilled = function CompassFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CompassFilledSvg
  }));
};

/**![compass](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0ek0zMjcuMyA3MDIuNGMtMiAuOS00LjQgMC01LjMtMi4xLS40LTEtLjQtMi4yIDAtMy4ybDk4LjctMjI1LjUgMTMyLjEgMTMyLjEtMjI1LjUgOTguN3ptMzc1LjEtMzc1LjFsLTk4LjcgMjI1LjUtMTMyLjEtMTMyLjFMNjk3LjEgMzIyYzItLjkgNC40IDAgNS4zIDIuMS40IDEgLjQgMi4xIDAgMy4yeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CompassFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CompassFilled';
}
export default RefIcon;