import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PhoneFilledSvg from "@ant-design/icons-svg/es/asn/PhoneFilled";
import AntdIcon from "../components/AntdIcon";
var PhoneFilled = function PhoneFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PhoneFilledSvg
  }));
};

/**![phone](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4NS42IDIzMC4yTDc3OS4xIDEyMy44YTgwLjgzIDgwLjgzIDAgMDAtNTcuMy0yMy44Yy0yMS43IDAtNDIuMSA4LjUtNTcuNCAyMy44TDU0OS44IDIzOC40YTgwLjgzIDgwLjgzIDAgMDAtMjMuOCA1Ny4zYzAgMjEuNyA4LjUgNDIuMSAyMy44IDU3LjRsODMuOCA4My44QTM5My44MiAzOTMuODIgMCAwMTU1My4xIDU1MyAzOTUuMzQgMzk1LjM0IDAgMDE0MzcgNjMzLjhMMzUzLjIgNTUwYTgwLjgzIDgwLjgzIDAgMDAtNTcuMy0yMy44Yy0yMS43IDAtNDIuMSA4LjUtNTcuNCAyMy44TDEyMy44IDY2NC41YTgwLjg5IDgwLjg5IDAgMDAtMjMuOCA1Ny40YzAgMjEuNyA4LjUgNDIuMSAyMy44IDU3LjRsMTA2LjMgMTA2LjNjMjQuNCAyNC41IDU4LjEgMzguNCA5Mi43IDM4LjQgNy4zIDAgMTQuMy0uNiAyMS4yLTEuOCAxMzQuOC0yMi4yIDI2OC41LTkzLjkgMzc2LjQtMjAxLjdDODI4LjIgNjEyLjggODk5LjggNDc5LjIgOTIyLjMgMzQ0YzYuOC00MS4zLTYuOS04My44LTM2LjctMTEzLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(PhoneFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PhoneFilled';
}
export default RefIcon;