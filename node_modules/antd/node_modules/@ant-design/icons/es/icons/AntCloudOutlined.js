import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AntCloudOutlinedSvg from "@ant-design/icons-svg/es/asn/AntCloudOutlined";
import AntdIcon from "../components/AntdIcon";
var AntCloudOutlined = function AntCloudOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AntCloudOutlinedSvg
  }));
};

/**![ant-cloud](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM3OC45IDczOGMtMy4xIDAtNi4xLS41LTguOC0xLjVsNC40IDMwLjdoMjYuM2wtMTUuNS0yOS45Yy0yLjEuNS00LjIuNy02LjQuN3ptNDIxLTI5MS4yYy0xMi42IDAtMjQuOCAxLjUtMzYuNSA0LjItMjEuNC0zOC40LTYyLjMtNjQuMy0xMDkuMy02NC4zLTYuOSAwLTEzLjYuNi0yMC4yIDEuNi0zNS40LTc3LjQtMTEzLjQtMTMxLjEtMjAzLjktMTMxLjEtMTEyLjMgMC0yMDUuMyA4Mi42LTIyMS42IDE5MC40QzEyNy4zIDQ1NS41IDY0IDUyMy44IDY0IDYwN2MwIDg4LjQgNzEuNiAxNjAuMSAxNjAgMTYwLjJoNTBsMTMuMi0yNy42Yy0yNi4yLTguMy00My4zLTI5LTM5LjEtNDguOCA0LjYtMjEuNiAzMi44LTMzLjkgNjMuMS0yNy41IDIyLjkgNC45IDQwLjQgMTkuMSA0NS41IDM1LjFhMjYuMSAyNi4xIDAgMDEyMi4xLTEyLjRoLjJjLS44LTMuMi0xLjItNi41LTEuMi05LjkgMC0yMC4xIDE0LjgtMzYuNyAzNC4xLTM5LjZ2LTI1LjRjMC00LjQgMy42LTggOC04czggMy42IDggOHYyNi4zYzQuNiAxLjIgOC44IDMuMiAxMi42IDUuOGwxOS41LTIxLjRjMy0zLjMgOC0zLjUgMTEuMy0uNSAzLjMgMyAzLjUgOCAuNSAxMS4zbC0yMCAyMi0uMi4yYTQwIDQwIDAgMDEtNDYuOSA1OS4yYy0uNCA1LjYtMi42IDEwLjctNiAxNC44bDIwIDM4LjRIODA0di0uMWM4Ni41LTIuMiAxNTYtNzMgMTU2LTE2MC4xIDAtODguNS03MS43LTE2MC4yLTE2MC4xLTE2MC4yek0zMzguMiA3MzcuMmwtNC4zIDMwaDI0LjRsLTUuOS00MS41Yy0zLjUgNC42LTguMyA4LjUtMTQuMiAxMS41ek03OTcuNSAzMDVhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0tNjUuNyA2MS4zYTI0IDI0IDAgMTA0OCAwIDI0IDI0IDAgMTAtNDggMHpNMzAzLjQgNzQyLjlsLTExLjYgMjQuM2gyNmwzLjUtMjQuN2MtNS43LjgtMTEuNyAxLTE3LjkuNHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(AntCloudOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AntCloudOutlined';
}
export default RefIcon;