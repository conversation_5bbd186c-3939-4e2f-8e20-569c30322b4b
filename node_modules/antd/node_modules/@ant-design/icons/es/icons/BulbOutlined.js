import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BulbOutlinedSvg from "@ant-design/icons-svg/es/asn/BulbOutlined";
import AntdIcon from "../components/AntdIcon";
var BulbOutlined = function BulbOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BulbOutlinedSvg
  }));
};

/**![bulb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYzMiA4ODhIMzkyYy00LjQgMC04IDMuNi04IDh2MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoMTkyYzE3LjcgMCAzMi0xNC4zIDMyLTMydi0zMmMwLTQuNC0zLjYtOC04LTh6TTUxMiA2NGMtMTgxLjEgMC0zMjggMTQ2LjktMzI4IDMyOCAwIDEyMS40IDY2IDIyNy40IDE2NCAyODQuMVY3OTJjMCAxNy43IDE0LjMgMzIgMzIgMzJoMjY0YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjY3Ni4xYzk4LTU2LjcgMTY0LTE2Mi43IDE2NC0yODQuMSAwLTE4MS4xLTE0Ni45LTMyOC0zMjgtMzI4em0xMjcuOSA1NDkuOEw2MDQgNjM0LjZWNzUySDQyMFY2MzQuNmwtMzUuOS0yMC44QzMwNS40IDU2OC4zIDI1NiA0ODQuNSAyNTYgMzkyYzAtMTQxLjQgMTE0LjYtMjU2IDI1Ni0yNTZzMjU2IDExNC42IDI1NiAyNTZjMCA5Mi41LTQ5LjQgMTc2LjMtMTI4LjEgMjIxLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BulbOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BulbOutlined';
}
export default RefIcon;