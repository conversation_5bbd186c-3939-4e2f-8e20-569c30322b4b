import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FundTwoToneSvg from "@ant-design/icons-svg/es/asn/FundTwoTone";
import AntdIcon from "../components/AntdIcon";
var FundTwoTone = function FundTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FundTwoToneSvg
  }));
};

/**![fund](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAxNjBIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTkyYzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNjMySDEzNlYyMzJoNzUydjU2MHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTEzNiA3OTJoNzUyVjIzMkgxMzZ2NTYwem01Ni40LTEzMC41bDIxNC45LTIxNWMzLjEtMy4xIDguMi0zLjEgMTEuMyAwTDUzMyA1NjFsMjU0LjUtMjU0LjZjMy4xLTMuMSA4LjItMy4xIDExLjMgMGwzNi44IDM2LjhjMy4xIDMuMSAzLjEgOC4yIDAgMTEuM2wtMjk3IDI5Ny4yYTguMDMgOC4wMyAwIDAxLTExLjMgMEw0MTIuOSA1MzcuMiAyNDAuNCA3MDkuN2E4LjAzIDguMDMgMCAwMS0xMS4zIDBsLTM2LjctMzYuOWE4LjAzIDguMDMgMCAwMTAtMTEuM3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTIyOS4xIDcwOS43YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsMTcyLjUtMTcyLjUgMTE0LjQgMTE0LjVjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGwyOTctMjk3LjJjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM2wtMzYuOC0zNi44YTguMDMgOC4wMyAwIDAwLTExLjMgMEw1MzMgNTYxIDQxOC42IDQ0Ni41YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMjE0LjkgMjE1YTguMDMgOC4wMyAwIDAwMCAxMS4zbDM2LjcgMzYuOXoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FundTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FundTwoTone';
}
export default RefIcon;