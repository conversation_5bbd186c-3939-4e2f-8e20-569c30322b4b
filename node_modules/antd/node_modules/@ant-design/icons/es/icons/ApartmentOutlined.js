import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ApartmentOutlinedSvg from "@ant-design/icons-svg/es/asn/ApartmentOutlined";
import AntdIcon from "../components/AntdIcon";
var ApartmentOutlined = function ApartmentOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ApartmentOutlinedSvg
  }));
};

/**![apartment](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwOCA2NDBIODA0VjQ4OGMwLTQuNC0zLjYtOC04LThINTQ4di05NmgxMDhjOC44IDAgMTYtNy4yIDE2LTE2VjgwYzAtOC44LTcuMi0xNi0xNi0xNkgzNjhjLTguOCAwLTE2IDcuMi0xNiAxNnYyODhjMCA4LjggNy4yIDE2IDE2IDE2aDEwOHY5NkgyMjhjLTQuNCAwLTggMy42LTggOHYxNTJIMTE2Yy04LjggMC0xNiA3LjItMTYgMTZ2Mjg4YzAgOC44IDcuMiAxNiAxNiAxNmgyODhjOC44IDAgMTYtNy4yIDE2LTE2VjY1NmMwLTguOC03LjItMTYtMTYtMTZIMjkydi04OGg0NDB2ODhINjIwYy04LjggMC0xNiA3LjItMTYgMTZ2Mjg4YzAgOC44IDcuMiAxNiAxNiAxNmgyODhjOC44IDAgMTYtNy4yIDE2LTE2VjY1NmMwLTguOC03LjItMTYtMTYtMTZ6bS01NjQgNzZ2MTY4SDE3NlY3MTZoMTY4em04NC00MDhWMTQwaDE2OHYxNjhINDI4em00MjAgNTc2SDY4MFY3MTZoMTY4djE2OHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(ApartmentOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ApartmentOutlined';
}
export default RefIcon;