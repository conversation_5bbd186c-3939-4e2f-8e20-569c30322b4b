import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TruckOutlinedSvg from "@ant-design/icons-svg/es/asn/TruckOutlined";
import AntdIcon from "../components/AntdIcon";
var TruckOutlined = function TruckOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TruckOutlinedSvg
  }));
};

/**![truck](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNjA4IDE5MmEzMiAzMiAwIDAxMzIgMzJ2MTYwaDE3NC44MWEzMiAzMiAwIDAxMjYuNjggMTQuMzNsMTEzLjE5IDE3MC44NGEzMiAzMiAwIDAxNS4zMiAxNy42OFY2NzJhMzIgMzIgMCAwMS0zMiAzMmgtOTZjMCA3MC43LTU3LjMgMTI4LTEyOCAxMjhzLTEyOC01Ny4zLTEyOC0xMjhIMzg0YzAgNzAuNy01Ny4zIDEyOC0xMjggMTI4cy0xMjgtNTcuMy0xMjgtMTI4SDk2YTMyIDMyIDAgMDEtMzItMzJWMjI0YTMyIDMyIDAgMDEzMi0zMnpNMjU2IDY0MGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwMjU2IDY0MG00NDggMGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwNzA0IDY0ME01NzYgMjU2SDEyOHYzODRoMTcuMTJjMjIuMTMtMzguMjYgNjMuNS02NCAxMTAuODgtNjQgNDcuMzggMCA4OC43NSAyNS43NCAxMTAuODggNjRINTc2em0yMjEuNjMgMTkySDY0MHYxNDUuMTJBMTI3LjQzIDEyNy40MyAwIDAxNzA0IDU3NmM0Ny4zOCAwIDg4Ljc1IDI1Ljc0IDExMC44OCA2NEg4OTZ2LTQzLjUyek01MDAgNDQ4YTEyIDEyIDAgMDExMiAxMnY0MGExMiAxMiAwIDAxLTEyIDEySDMzMmExMiAxMiAwIDAxLTEyLTEydi00MGExMiAxMiAwIDAxMTItMTJ6TTMwOCAzMjBhMTIgMTIgMCAwMTEyIDEydjQwYTEyIDEyIDAgMDEtMTIgMTJIMjA0YTEyIDEyIDAgMDEtMTItMTJ2LTQwYTEyIDEyIDAgMDExMi0xMnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(TruckOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TruckOutlined';
}
export default RefIcon;