import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MonitorOutlinedSvg from "@ant-design/icons-svg/es/asn/MonitorOutlined";
import AntdIcon from "../components/AntdIcon";
var MonitorOutlined = function MonitorOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MonitorOutlinedSvg
  }));
};

/**![monitor](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5Mi44IDQxMi43bC4yLS4yLTM0LjYtNDQuM2E3Ljk3IDcuOTcgMCAwMC0xMS4yLTEuNGwtNTAuNCAzOS4zLTcwLjUtOTAuMWE3Ljk3IDcuOTcgMCAwMC0xMS4yLTEuNGwtMzcuOSAyOS43YTcuOTcgNy45NyAwIDAwLTEuNCAxMS4ybDcwLjUgOTAuMi0uMi4xIDM0LjYgNDQuM2MyLjcgMy41IDcuNyA0LjEgMTEuMiAxLjRsNTAuNC0zOS4zIDY0LjEgODJjMi43IDMuNSA3LjcgNC4xIDExLjIgMS40bDM3LjktMjkuNmMzLjUtMi43IDQuMS03LjcgMS40LTExLjJsLTY0LjEtODIuMXpNNjA4IDExMmMtMTY3LjkgMC0zMDQgMTM2LjEtMzA0IDMwNCAwIDcwLjMgMjMuOSAxMzUgNjMuOSAxODYuNUwxMTQuMyA4NTYuMWE4LjAzIDguMDMgMCAwMDAgMTEuM2w0Mi4zIDQyLjNjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGwyNTMuNi0yNTMuNkM0NzMgNjk2LjEgNTM3LjcgNzIwIDYwOCA3MjBjMTY3LjkgMCAzMDQtMTM2LjEgMzA0LTMwNFM3NzUuOSAxMTIgNjA4IDExMnptMTYxLjIgNDY1LjJDNzI2LjIgNjIwLjMgNjY4LjkgNjQ0IDYwOCA2NDRzLTExOC4yLTIzLjctMTYxLjItNjYuOEM0MDMuNyA1MzQuMiAzODAgNDc2LjkgMzgwIDQxNnMyMy43LTExOC4yIDY2LjgtMTYxLjJjNDMtNDMuMSAxMDAuMy02Ni44IDE2MS4yLTY2LjhzMTE4LjIgMjMuNyAxNjEuMiA2Ni44YzQzLjEgNDMgNjYuOCAxMDAuMyA2Ni44IDE2MS4ycy0yMy43IDExOC4yLTY2LjggMTYxLjJ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MonitorOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MonitorOutlined';
}
export default RefIcon;