import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MutedFilledSvg from "@ant-design/icons-svg/es/asn/MutedFilled";
import AntdIcon from "../components/AntdIcon";
var MutedFilled = function MutedFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MutedFilledSvg
  }));
};

/**![muted](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNzcxLjkxIDExNWEzMS42NSAzMS42NSAwIDAwLTE3LjQyIDUuMjdMNDAwIDM1MS45N0gyMzZhMTYgMTYgMCAwMC0xNiAxNnYyODguMDZhMTYgMTYgMCAwMDE2IDE2aDE2NGwzNTQuNSAyMzEuN2EzMS42NiAzMS42NiAwIDAwMTcuNDIgNS4yN2MxNi42NSAwIDMyLjA4LTEzLjI1IDMyLjA4LTMyLjA2VjE0Ny4wNmMwLTE4LjgtMTUuNDQtMzIuMDYtMzIuMDktMzIuMDYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(MutedFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MutedFilled';
}
export default RefIcon;