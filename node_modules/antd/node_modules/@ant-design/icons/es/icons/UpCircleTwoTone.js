import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UpCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/UpCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
var UpCircleTwoTone = function UpCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: UpCircleTwoToneSvg
  }));
};

/**![up-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTE3OCA0NzloLTQ2LjljLTEwLjIgMC0xOS45LTQuOS0yNS45LTEzLjJMNTEyIDQ2MC40IDQwNi44IDYwNS44Yy02IDguMy0xNS42IDEzLjItMjUuOSAxMy4ySDMzNGMtNi41IDAtMTAuMy03LjQtNi41LTEyLjdsMTc4LTI0NmMzLjItNC40IDkuNy00LjQgMTIuOSAwbDE3OCAyNDZjMy45IDUuMy4xIDEyLjctNi40IDEyLjd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MTIgNjRDMjY0LjYgNjQgNjQgMjY0LjYgNjQgNTEyczIwMC42IDQ0OCA0NDggNDQ4IDQ0OC0yMDAuNiA0NDgtNDQ4Uzc1OS40IDY0IDUxMiA2NHptMCA4MjBjLTIwNS40IDAtMzcyLTE2Ni42LTM3Mi0zNzJzMTY2LjYtMzcyIDM3Mi0zNzIgMzcyIDE2Ni42IDM3MiAzNzItMTY2LjYgMzcyLTM3MiAzNzJ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik01MTguNCAzNjAuM2E3Ljk1IDcuOTUgMCAwMC0xMi45IDBsLTE3OCAyNDZjLTMuOCA1LjMgMCAxMi43IDYuNSAxMi43aDQ2LjljMTAuMyAwIDE5LjktNC45IDI1LjktMTMuMkw1MTIgNDYwLjRsMTA1LjIgMTQ1LjRjNiA4LjMgMTUuNyAxMy4yIDI1LjkgMTMuMkg2OTBjNi41IDAgMTAuMy03LjQgNi40LTEyLjdsLTE3OC0yNDZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(UpCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpCircleTwoTone';
}
export default RefIcon;