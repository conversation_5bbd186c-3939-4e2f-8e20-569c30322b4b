import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SmileFilledSvg from "@ant-design/icons-svg/es/asn/SmileFilled";
import AntdIcon from "../components/AntdIcon";
var SmileFilled = function SmileFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SmileFilledSvg
  }));
};

/**![smile](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0ek0yODggNDIxYTQ4LjAxIDQ4LjAxIDAgMDE5NiAwIDQ4LjAxIDQ4LjAxIDAgMDEtOTYgMHptMjI0IDI3MmMtODUuNSAwLTE1NS42LTY3LjMtMTYwLTE1MS42YTggOCAwIDAxOC04LjRoNDguMWM0LjIgMCA3LjggMy4yIDguMSA3LjRDNDIwIDU4OS45IDQ2MS41IDYyOSA1MTIgNjI5czkyLjEtMzkuMSA5NS44LTg4LjZjLjMtNC4yIDMuOS03LjQgOC4xLTcuNEg2NjRhOCA4IDAgMDE4IDguNEM2NjcuNiA2MjUuNyA1OTcuNSA2OTMgNTEyIDY5M3ptMTc2LTIyNGE0OC4wMSA0OC4wMSAwIDAxMC05NiA0OC4wMSA0OC4wMSAwIDAxMCA5NnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(SmileFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SmileFilled';
}
export default RefIcon;