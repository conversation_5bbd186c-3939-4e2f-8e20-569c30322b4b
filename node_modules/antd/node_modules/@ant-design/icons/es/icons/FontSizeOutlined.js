import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FontSizeOutlinedSvg from "@ant-design/icons-svg/es/asn/FontSizeOutlined";
import AntdIcon from "../components/AntdIcon";
var FontSizeOutlined = function FontSizeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FontSizeOutlinedSvg
  }));
};

/**![font-size](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyMCA0MTZINjE2Yy00LjQgMC04IDMuNi04IDh2MTEyYzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LTh2LTU2aDYwdjMyMGgtNDZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTY0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04aC00NlY0ODBoNjB2NTZjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOFY0MjRjMC00LjQtMy42LTgtOC04ek02NTYgMjk2VjE2OGMwLTQuNC0zLjYtOC04LThIMTA0Yy00LjQgMC04IDMuNi04IDh2MTI4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTY0aDE2OHY1NjBoLTkyYy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDI2NGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOGgtOTJWMjMyaDE2OHY2NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FontSizeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FontSizeOutlined';
}
export default RefIcon;