import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ColumnWidthOutlinedSvg from "@ant-design/icons-svg/es/asn/ColumnWidthOutlined";
import AntdIcon from "../components/AntdIcon";
var ColumnWidthOutlined = function ColumnWidthOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ColumnWidthOutlinedSvg
  }));
};

/**![column-width](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4MCAxNzZoLTYwYy00LjQgMC04IDMuNi04IDh2NjU2YzAgNC40IDMuNiA4IDggOGg2MGM0LjQgMCA4LTMuNiA4LThWMTg0YzAtNC40LTMuNi04LTgtOHptNzI0IDBoLTYwYy00LjQgMC04IDMuNi04IDh2NjU2YzAgNC40IDMuNiA4IDggOGg2MGM0LjQgMCA4LTMuNiA4LThWMTg0YzAtNC40LTMuNi04LTgtOHpNNzg1LjMgNTA0LjNMNjU3LjcgNDAzLjZhNy4yMyA3LjIzIDAgMDAtMTEuNyA1LjdWNDc2SDM3OHYtNjIuOGMwLTYtNy05LjQtMTEuNy01LjdMMjM4LjcgNTA4LjNhNy4xNCA3LjE0IDAgMDAwIDExLjNsMTI3LjUgMTAwLjhjNC43IDMuNyAxMS43LjQgMTEuNy01LjdWNTQ4aDI2OHY2Mi44YzAgNiA3IDkuNCAxMS43IDUuN2wxMjcuNS0xMDAuOGMzLjgtMi45IDMuOC04LjUuMi0xMS40eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ColumnWidthOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ColumnWidthOutlined';
}
export default RefIcon;