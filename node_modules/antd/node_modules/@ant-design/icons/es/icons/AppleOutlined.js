import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AppleOutlinedSvg from "@ant-design/icons-svg/es/asn/AppleOutlined";
import AntdIcon from "../components/AntdIcon";
var AppleOutlined = function AppleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AppleOutlinedSvg
  }));
};

/**![apple](data:image/svg+xml;base64,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) */
var RefIcon = /*#__PURE__*/React.forwardRef(AppleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AppleOutlined';
}
export default RefIcon;