import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloseCircleFilledSvg from "@ant-design/icons-svg/es/asn/CloseCircleFilled";
import AntdIcon from "../components/AntdIcon";
var CloseCircleFilled = function CloseCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloseCircleFilledSvg
  }));
};

/**![close-circle](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloseCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloseCircleFilled';
}
export default RefIcon;