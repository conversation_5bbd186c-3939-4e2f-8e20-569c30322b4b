import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SlackCircleFilledSvg from "@ant-design/icons-svg/es/asn/SlackCircleFilled";
import AntdIcon from "../components/AntdIcon";
var SlackCircleFilled = function SlackCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SlackCircleFilledSvg
  }));
};

/**![slack-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0ek0zNjEuNSA1ODAuMmMwIDI3LjgtMjIuNSA1MC40LTUwLjMgNTAuNGE1MC4zNSA1MC4zNSAwIDAxLTUwLjMtNTAuNGMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGg1MC4zdjUwLjR6bTEzNCAxMzQuNGMwIDI3LjgtMjIuNSA1MC40LTUwLjMgNTAuNC0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNFY1ODAuMmMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGE1MC4zNSA1MC4zNSAwIDAxNTAuMyA1MC40djEzNC40em0tNTAuMi0yMTguNGgtMTM0Yy0yNy44IDAtNTAuMy0yMi42LTUwLjMtNTAuNCAwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNGgxMzRjMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjQtLjEgMjcuOS0yMi42IDUwLjQtNTAuMyA1MC40em0wLTEzNC40Yy0xMy4zIDAtMjYuMS01LjMtMzUuNi0xNC44UzM5NSAzMjQuOCAzOTUgMzExLjRjMC0yNy44IDIyLjUtNTAuNCA1MC4zLTUwLjQgMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjR2NTAuNGgtNTAuM3ptODMuNy01MC40YzAtMjcuOCAyMi41LTUwLjQgNTAuMy01MC40IDI3LjggMCA1MC4zIDIyLjYgNTAuMyA1MC40djEzNC40YzAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40LTI3LjggMC01MC4zLTIyLjYtNTAuMy01MC40VjMxMS40ek01NzkuMyA3NjVjLTI3LjggMC01MC4zLTIyLjYtNTAuMy01MC40di01MC40aDUwLjNjMjcuOCAwIDUwLjMgMjIuNiA1MC4zIDUwLjQgMCAyNy44LTIyLjUgNTAuNC01MC4zIDUwLjR6bTEzNC0xMzQuNGgtMTM0Yy0xMy4zIDAtMjYuMS01LjMtMzUuNi0xNC44UzUyOSA1OTMuNiA1MjkgNTgwLjJjMC0yNy44IDIyLjUtNTAuNCA1MC4zLTUwLjRoMTM0YzI3LjggMCA1MC4zIDIyLjYgNTAuMyA1MC40IDAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40em0wLTEzNC40SDY2M3YtNTAuNGMwLTI3LjggMjIuNS01MC40IDUwLjMtNTAuNHM1MC4zIDIyLjYgNTAuMyA1MC40YzAgMjcuOC0yMi41IDUwLjQtNTAuMyA1MC40eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(SlackCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SlackCircleFilled';
}
export default RefIcon;