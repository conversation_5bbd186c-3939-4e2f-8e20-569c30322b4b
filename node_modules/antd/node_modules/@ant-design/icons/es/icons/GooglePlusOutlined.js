import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import GooglePlusOutlinedSvg from "@ant-design/icons-svg/es/asn/GooglePlusOutlined";
import AntdIcon from "../components/AntdIcon";
var GooglePlusOutlined = function GooglePlusOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: GooglePlusOutlinedSvg
  }));
};

/**![google-plus](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(GooglePlusOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GooglePlusOutlined';
}
export default RefIcon;