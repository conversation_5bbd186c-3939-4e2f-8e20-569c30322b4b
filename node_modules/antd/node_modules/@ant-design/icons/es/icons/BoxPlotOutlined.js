import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BoxPlotOutlinedSvg from "@ant-design/icons-svg/es/asn/BoxPlotOutlined";
import AntdIcon from "../components/AntdIcon";
var BoxPlotOutlined = function BoxPlotOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BoxPlotOutlinedSvg
  }));
};

/**![box-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiAyMjRoLTUyYy00LjQgMC04IDMuNi04IDh2MjQ4aC05MlYzMDRjMC00LjQtMy42LTgtOC04SDIzMmMtNC40IDAtOCAzLjYtOCA4djE3NmgtOTJWMjMyYzAtNC40LTMuNi04LTgtOEg3MmMtNC40IDAtOCAzLjYtOCA4djU2MGMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjU0OGg5MnYxNzJjMCA0LjQgMy42IDggOCA4aDU2MGM0LjQgMCA4LTMuNiA4LThWNTQ4aDkydjI0NGMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjIzMmMwLTQuNC0zLjYtOC04LTh6TTI5NiAzNjhoODh2Mjg4aC04OFYzNjh6bTQzMiAyODhINDQ4VjM2OGgyODB2Mjg4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(BoxPlotOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BoxPlotOutlined';
}
export default RefIcon;