import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FileWordOutlinedSvg from "@ant-design/icons-svg/es/asn/FileWordOutlined";
import AntdIcon from "../components/AntdIcon";
var FileWordOutlined = function FileWordOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FileWordOutlinedSvg
  }));
};

/**![file-word](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHpNNTI4LjEgNDcyaC0zMi4yYy01LjUgMC0xMC4zIDMuNy0xMS42IDkuMUw0MzQuNiA2ODBsLTQ2LjEtMTk4LjdjLTEuMy01LjQtNi4xLTkuMy0xMS43LTkuM2gtMzUuNGExMi4wMiAxMi4wMiAwIDAwLTExLjYgMTUuMWw3NC4yIDI3NmMxLjQgNS4yIDYuMiA4LjkgMTEuNiA4LjloMzJjNS40IDAgMTAuMi0zLjYgMTEuNi04LjlsNTIuOC0xOTcgNTIuOCAxOTdjMS40IDUuMiA2LjIgOC45IDExLjYgOC45aDMxLjhjNS40IDAgMTAuMi0zLjYgMTEuNi04LjlsNzQuNC0yNzZhMTIuMDQgMTIuMDQgMCAwMC0xMS42LTE1LjFINjQ3Yy01LjYgMC0xMC40IDMuOS0xMS43IDkuM2wtNDUuOCAxOTkuMS00OS44LTE5OS4zYy0xLjMtNS40LTYuMS05LjEtMTEuNi05LjF6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileWordOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileWordOutlined';
}
export default RefIcon;