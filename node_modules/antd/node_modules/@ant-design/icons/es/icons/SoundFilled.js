import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SoundFilledSvg from "@ant-design/icons-svg/es/asn/SoundFilled";
import AntdIcon from "../components/AntdIcon";
var SoundFilled = function SoundFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SoundFilledSvg
  }));
};

/**![sound](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5Mi4xIDczNy44bC0xMTAuMy02My43YTE1LjkgMTUuOSAwIDAwLTIxLjcgNS45bC0xOS45IDM0LjVjLTQuNCA3LjYtMS44IDE3LjQgNS44IDIxLjhMODU2LjMgODAwYTE1LjkgMTUuOSAwIDAwMjEuNy01LjlsMTkuOS0zNC41YzQuNC03LjYgMS43LTE3LjQtNS44LTIxLjh6TTc2MCAzNDRhMTUuOSAxNS45IDAgMDAyMS43IDUuOUw4OTIgMjg2LjJjNy42LTQuNCAxMC4yLTE0LjIgNS44LTIxLjhMODc4IDIzMGExNS45IDE1LjkgMCAwMC0yMS43LTUuOUw3NDYgMjg3LjhhMTUuOTkgMTUuOTkgMCAwMC01LjggMjEuOEw3NjAgMzQ0em0xNzQgMTMySDgwNmMtOC44IDAtMTYgNy4yLTE2IDE2djQwYzAgOC44IDcuMiAxNiAxNiAxNmgxMjhjOC44IDAgMTYtNy4yIDE2LTE2di00MGMwLTguOC03LjItMTYtMTYtMTZ6TTYyNS45IDExNWMtNS45IDAtMTEuOSAxLjYtMTcuNCA1LjNMMjU0IDM1Mkg5MGMtOC44IDAtMTYgNy4yLTE2IDE2djI4OGMwIDguOCA3LjIgMTYgMTYgMTZoMTY0bDM1NC41IDIzMS43YzUuNSAzLjYgMTEuNiA1LjMgMTcuNCA1LjMgMTYuNyAwIDMyLjEtMTMuMyAzMi4xLTMyLjFWMTQ3LjFjMC0xOC44LTE1LjQtMzIuMS0zMi4xLTMyLjF6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SoundFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SoundFilled';
}
export default RefIcon;