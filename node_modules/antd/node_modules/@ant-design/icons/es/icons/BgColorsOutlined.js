import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BgColorsOutlinedSvg from "@ant-design/icons-svg/es/asn/BgColorsOutlined";
import AntdIcon from "../components/AntdIcon";
var BgColorsOutlined = function BgColorsOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BgColorsOutlinedSvg
  }));
};

/**![bg-colors](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc2Ni40IDc0NC4zYzQzLjcgMCA3OS40LTM2LjIgNzkuNC04MC41IDAtNTMuNS03OS40LTE0MC44LTc5LjQtMTQwLjhTNjg3IDYxMC4zIDY4NyA2NjMuOGMwIDQ0LjMgMzUuNyA4MC41IDc5LjQgODAuNXptLTM3Ny4xLTQ0LjFjNy4xIDcuMSAxOC42IDcuMSAyNS42IDBsMjU2LjEtMjU2YzcuMS03LjEgNy4xLTE4LjYgMC0yNS42bC0yNTYtMjU2Yy0uNi0uNi0xLjMtMS4yLTItMS43bC03OC4yLTc4LjJhOS4xMSA5LjExIDAgMDAtMTIuOCAwbC00OCA0OGE5LjExIDkuMTEgMCAwMDAgMTIuOGw2Ny4yIDY3LjItMjA3LjggMjA3LjljLTcuMSA3LjEtNy4xIDE4LjYgMCAyNS42bDI1NS45IDI1NnptMTIuOS00NDguNmwxNzguOSAxNzguOUgyMjMuNGwxNzguOC0xNzguOXpNOTA0IDgxNkgxMjBjLTQuNCAwLTggMy42LTggOHY4MGMwIDQuNCAzLjYgOCA4IDhoNzg0YzQuNCAwIDgtMy42IDgtOHYtODBjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(BgColorsOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BgColorsOutlined';
}
export default RefIcon;