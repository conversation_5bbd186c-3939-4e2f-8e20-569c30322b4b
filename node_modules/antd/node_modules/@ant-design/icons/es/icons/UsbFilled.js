import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UsbFilledSvg from "@ant-design/icons-svg/es/asn/UsbFilled";
import AntdIcon from "../components/AntdIcon";
var UsbFilled = function UsbFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: UsbFilledSvg
  }));
};

/**![usb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQwOCAzMTJoNDhjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LThoLTQ4Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4em0zNTIgMTIwVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMkgyOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjI4OGMtNjYuMiAwLTEyMCA1Mi4xLTEyMCAxMTZ2MzU2YzAgNC40IDMuNiA4IDggOGg3MjBjNC40IDAgOC0zLjYgOC04VjU0OGMwLTYzLjktNTMuOC0xMTYtMTIwLTExNnptLTcyIDBIMzM2VjE4NGgzNTJ2MjQ4ek01NjggMzEyaDQ4YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(UsbFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UsbFilled';
}
export default RefIcon;