import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import DeleteTwoToneSvg from "@ant-design/icons-svg/es/asn/DeleteTwoTone";
import AntdIcon from "../components/AntdIcon";
var DeleteTwoTone = function DeleteTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: DeleteTwoToneSvg
  }));
};

/**![delete](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5Mi43IDg0MGg0MzguNmwyNC4yLTUxMmgtNDg3eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODY0IDI1Nkg3MzZ2LTgwYzAtMzUuMy0yOC43LTY0LTY0LTY0SDM1MmMtMzUuMyAwLTY0IDI4LjctNjQgNjR2ODBIMTYwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYzMmMwIDQuNCAzLjYgOCA4IDhoNjAuNGwyNC43IDUyM2MxLjYgMzQuMSAyOS44IDYxIDYzLjkgNjFoNDU0YzM0LjIgMCA2Mi4zLTI2LjggNjMuOS02MWwyNC43LTUyM0g4ODhjNC40IDAgOC0zLjYgOC04di0zMmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTUwNC03MmgzMDR2NzJIMzYwdi03MnptMzcxLjMgNjU2SDI5Mi43bC0yNC4yLTUxMmg0ODdsLTI0LjIgNTEyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(DeleteTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DeleteTwoTone';
}
export default RefIcon;