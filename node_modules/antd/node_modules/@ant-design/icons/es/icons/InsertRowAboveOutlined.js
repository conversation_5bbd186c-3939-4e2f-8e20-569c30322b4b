import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import InsertRowAboveOutlinedSvg from "@ant-design/icons-svg/es/asn/InsertRowAboveOutlined";
import AntdIcon from "../components/AntdIcon";
var InsertRowAboveOutlined = function InsertRowAboveOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: InsertRowAboveOutlinedSvg
  }));
};

/**![insert-row-above](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NzguNyAzMzZIMTQ1LjNjLTE4LjQgMC0zMy4zIDE0LjMtMzMuMyAzMnY0NjRjMCAxNy43IDE0LjkgMzIgMzMuMyAzMmg3MzMuM2MxOC40IDAgMzMuMy0xNC4zIDMzLjMtMzJWMzY4Yy4xLTE3LjctMTQuOC0zMi0zMy4yLTMyek0zNjAgNzkySDE4NFY2MzJoMTc2djE2MHptMC0yMjRIMTg0VjQwOGgxNzZ2MTYwem0yNDAgMjI0SDQyNFY2MzJoMTc2djE2MHptMC0yMjRINDI0VjQwOGgxNzZ2MTYwem0yNDAgMjI0SDY2NFY2MzJoMTc2djE2MHptMC0yMjRINjY0VjQwOGgxNzZ2MTYwem02NC00MDhIMTIwYy00LjQgMC04IDMuNi04IDh2ODBjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTgwYzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(InsertRowAboveOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InsertRowAboveOutlined';
}
export default RefIcon;