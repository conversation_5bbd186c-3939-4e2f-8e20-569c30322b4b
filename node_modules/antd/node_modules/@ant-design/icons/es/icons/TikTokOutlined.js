import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TikTokOutlinedSvg from "@ant-design/icons-svg/es/asn/TikTokOutlined";
import AntdIcon from "../components/AntdIcon";
var TikTokOutlined = function TikTokOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TikTokOutlinedSvg
  }));
};

/**![tik-tok](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTMwLjAxIDExMi42N2M0My42Ny0uNjcgODctLjM0IDEzMC4zMy0uNjcgMi42NyA1MSAyMSAxMDMgNTguMzMgMTM5IDM3LjMzIDM3IDkwIDU0IDE0MS4zMyA1OS42NlY0NDVjLTQ4LTEuNjctOTYuMzMtMTEuNjctMTQwLTMyLjM0LTE5LTguNjYtMzYuNjYtMTkuNjYtNTQtMzEtLjMzIDk3LjMzLjM0IDE5NC42Ny0uNjYgMjkxLjY3LTIuNjcgNDYuNjYtMTggOTMtNDUgMTMxLjMzLTQzLjY2IDY0LTExOS4zMiAxMDUuNjYtMTk2Ljk5IDEwNy00Ny42NiAyLjY2LTk1LjMzLTEwLjM0LTEzNi0zNC4zNEMyMjAuMDQgODM3LjY2IDE3Mi43IDc2NSAxNjUuNyA2ODdjLS42Ny0xNi42Ni0xLTMzLjMzLS4zNC00OS42NiA2LTYzLjM0IDM3LjMzLTEyNCA4Ni0xNjUuMzQgNTUuMzMtNDggMTMyLjY2LTcxIDIwNC45OS01Ny4zMy42NyA0OS4zNC0xLjMzIDk4LjY3LTEuMzMgMTQ4LTMzLTEwLjY3LTcxLjY3LTcuNjctMTAwLjY3IDEyLjMzLTIxIDEzLjY3LTM3IDM0LjY3LTQ1LjMzIDU4LjM0LTcgMTctNSAzNS42Ni00LjY2IDUzLjY2IDggNTQuNjcgNjAuNjYgMTAwLjY3IDExNi42NiA5NS42NyAzNy4zMy0uMzQgNzMtMjIgOTIuMzMtNTMuNjcgNi4zMy0xMSAxMy4zMy0yMi4zMyAxMy42Ni0zNS4zMyAzLjM0LTU5LjY3IDItMTE5IDIuMzQtMTc4LjY2LjMzLTEzNC4zNC0uMzQtMjY4LjMzLjY2LTQwMi4zMyIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(TikTokOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TikTokOutlined';
}
export default RefIcon;