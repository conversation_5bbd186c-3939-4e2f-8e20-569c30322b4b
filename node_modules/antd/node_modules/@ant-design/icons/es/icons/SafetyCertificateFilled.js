import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SafetyCertificateFilledSvg from "@ant-design/icons-svg/es/asn/SafetyCertificateFilled";
import AntdIcon from "../components/AntdIcon";
var SafetyCertificateFilled = function SafetyCertificateFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SafetyCertificateFilledSvg
  }));
};

/**![safety-certificate](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNNjk0LjUgMzQwLjdMNDgxLjkgNjMzLjRhMTYuMSAxNi4xIDAgMDEtMjYgMGwtMTI2LjQtMTc0Yy0zLjgtNS4zIDAtMTIuNyA2LjUtMTIuN2g1NS4yYzUuMSAwIDEwIDIuNSAxMyA2LjZsNjQuNyA4OSAxNTAuOS0yMDcuOGMzLTQuMSA3LjgtNi42IDEzLTYuNkg2ODhjNi41LjEgMTAuMyA3LjUgNi41IDEyLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SafetyCertificateFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SafetyCertificateFilled';
}
export default RefIcon;