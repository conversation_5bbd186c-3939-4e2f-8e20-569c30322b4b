import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AliyunOutlinedSvg from "@ant-design/icons-svg/es/asn/<PERSON>yunOutlined";
import AntdIcon from "../components/AntdIcon";
var AliyunOutlined = function AliyunOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AliyunOutlinedSvg
  }));
};

/**![aliyun](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1OS4yIDM4My45Yy0uMy04Mi4xLTY2LjktMTQ4LjYtMTQ5LjEtMTQ4LjZINTc1LjlsMjEuNiA4NS4yIDIwMSA0My43YTQyLjU4IDQyLjU4IDAgMDEzMi45IDM5LjdjLjEuNS4xIDIxNi4xIDAgMjE2LjZhNDIuNTggNDIuNTggMCAwMS0zMi45IDM5LjdsLTIwMSA0My43LTIxLjYgODUuM2gyMzQuMmM4Mi4xIDAgMTQ4LjgtNjYuNSAxNDkuMS0xNDguNlYzODMuOXpNMjI1LjUgNjYwLjRhNDIuNTggNDIuNTggMCAwMS0zMi45LTM5LjdjLS4xLS42LS4xLTIxNi4xIDAtMjE2LjYuOC0xOS40IDE0LjYtMzUuNSAzMi45LTM5LjdsMjAxLTQzLjcgMjEuNi04NS4ySDIxMy44Yy04Mi4xIDAtMTQ4LjggNjYuNC0xNDkuMSAxNDguNlY2NDFjLjMgODIuMSA2NyAxNDguNiAxNDkuMSAxNDguNkg0NDhsLTIxLjYtODUuMy0yMDAuOS00My45em0yMDAuOS0xNTguOGgxNzF2MjEuM2gtMTcxeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(AliyunOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AliyunOutlined';
}
export default RefIcon;