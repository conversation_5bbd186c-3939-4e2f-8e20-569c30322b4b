import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BankFilledSvg from "@ant-design/icons-svg/es/asn/BankFilled";
import AntdIcon from "../components/AntdIcon";
var BankFilled = function BankFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BankFilledSvg
  }));
};

/**![bank](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5NCA0NjJjMzAuOSAwIDQzLjgtMzkuNyAxOC43LTU4TDUzMC44IDEyNi4yYTMxLjgxIDMxLjgxIDAgMDAtMzcuNiAwTDExMS4zIDQwNGMtMjUuMSAxOC4yLTEyLjIgNTggMTguOCA1OEgxOTJ2Mzc0aC03MmMtNC40IDAtOCAzLjYtOCA4djUyYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01MmMwLTQuNC0zLjYtOC04LThoLTcyVjQ2Mmg2MnpNMzgxIDgzNkgyNjRWNDYyaDExN3YzNzR6bTE4OSAwSDQ1M1Y0NjJoMTE3djM3NHptMTkwIDBINjQyVjQ2MmgxMTh2Mzc0eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(BankFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BankFilled';
}
export default RefIcon;