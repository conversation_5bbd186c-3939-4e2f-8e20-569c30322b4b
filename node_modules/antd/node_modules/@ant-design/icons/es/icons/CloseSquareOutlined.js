import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloseSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/CloseSquareOutlined";
import AntdIcon from "../components/AntdIcon";
var CloseSquareOutlined = function CloseSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloseSquareOutlinedSvg
  }));
};

/**![close-square](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloseSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloseSquareOutlined';
}
export default RefIcon;