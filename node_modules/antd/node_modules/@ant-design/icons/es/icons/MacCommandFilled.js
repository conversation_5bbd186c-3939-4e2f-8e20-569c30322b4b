import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MacCommandFilledSvg from "@ant-design/icons-svg/es/asn/MacCommandFilled";
import AntdIcon from "../components/AntdIcon";
var MacCommandFilled = function MacCommandFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MacCommandFilledSvg
  }));
};

/**![mac-command](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik02MjQgNjcyYTQ4LjAxIDQ4LjAxIDAgMDA5NiAwYzAtMjYuNS0yMS41LTQ4LTQ4LTQ4aC00OHY0OHptOTYtMzIwYTQ4LjAxIDQ4LjAxIDAgMDAtOTYgMHY0OGg0OGMyNi41IDAgNDgtMjEuNSA0OC00OHoiIC8+PHBhdGggZD0iTTkyOCA2NEg5NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDgzMmMxNy43IDAgMzItMTQuMyAzMi0zMlY5NmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjcyIDU2MGM2MS45IDAgMTEyIDUwLjEgMTEyIDExMnMtNTAuMSAxMTItMTEyIDExMi0xMTItNTAuMS0xMTItMTEydi00OGgtOTZ2NDhjMCA2MS45LTUwLjEgMTEyLTExMiAxMTJzLTExMi01MC4xLTExMi0xMTIgNTAuMS0xMTIgMTEyLTExMmg0OHYtOTZoLTQ4Yy02MS45IDAtMTEyLTUwLjEtMTEyLTExMnM1MC4xLTExMiAxMTItMTEyIDExMiA1MC4xIDExMiAxMTJ2NDhoOTZ2LTQ4YzAtNjEuOSA1MC4xLTExMiAxMTItMTEyczExMiA1MC4xIDExMiAxMTItNTAuMSAxMTItMTEyIDExMmgtNDh2OTZoNDh6IiAvPjxwYXRoIGQ9Ik00NjQgNDY0aDk2djk2aC05NnpNMzUyIDMwNGE0OC4wMSA0OC4wMSAwIDAwMCA5Nmg0OHYtNDhjMC0yNi41LTIxLjUtNDgtNDgtNDh6bS00OCAzNjhhNDguMDEgNDguMDEgMCAwMDk2IDB2LTQ4aC00OGMtMjYuNSAwLTQ4IDIxLjUtNDggNDh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MacCommandFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MacCommandFilled';
}
export default RefIcon;