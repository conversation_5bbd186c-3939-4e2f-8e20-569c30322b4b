"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _PushpinFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/PushpinFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var PushpinFilled = function PushpinFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _PushpinFilled.default
  }));
};

/**![pushpin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3OC4zIDM5Mi4xTDYzMS45IDE0NS43Yy02LjUtNi41LTE1LTkuNy0yMy41LTkuN3MtMTcgMy4yLTIzLjUgOS43TDQyMy44IDMwNi45Yy0xMi4yLTEuNC0yNC41LTItMzYuOC0yLTczLjIgMC0xNDYuNCAyNC4xLTIwNi41IDcyLjMtMTUuNCAxMi4zLTE2LjYgMzUuNC0yLjcgNDkuNGwxODEuNyAxODEuNy0yMTUuNCAyMTUuMmExNS44IDE1LjggMCAwMC00LjYgOS44bC0zLjQgMzcuMmMtLjkgOS40IDYuNiAxNy40IDE1LjkgMTcuNC41IDAgMSAwIDEuNS0uMWwzNy4yLTMuNGMzLjctLjMgNy4yLTIgOS44LTQuNmwyMTUuNC0yMTUuNCAxODEuNyAxODEuN2M2LjUgNi41IDE1IDkuNyAyMy41IDkuNyA5LjcgMCAxOS4zLTQuMiAyNS45LTEyLjQgNTYuMy03MC4zIDc5LjctMTU4LjMgNzAuMi0yNDMuNGwxNjEuMS0xNjEuMWMxMi45LTEyLjggMTIuOS0zMy44IDAtNDYuOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PushpinFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PushpinFilled';
}
var _default = exports.default = RefIcon;