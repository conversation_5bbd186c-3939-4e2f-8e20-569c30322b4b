import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![snippets](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAxMTJINzI0VjcyYzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY0MEg1MDBWNzJjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djQwSDMyMGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTIwaC05NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NjMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDUxMmMxNy43IDAgMzItMTQuMyAzMi0zMnYtOTZoOTZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyek02NjQgODg4SDIzMlYzMzZoMjE4djE3NGMwIDIyLjEgMTcuOSA0MCA0MCA0MGgxNzR2MzM4em0wLTQwMkg1MTRWMzM2aC4yTDY2NCA0ODUuOHYuMnptMTI4IDI3NGgtNTZWNDU2TDU0NCAyNjRIMzYwdi04MGg2OHYzMmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di0zMmgxNTJ2MzJjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtMzJoNjh2NTc2eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
