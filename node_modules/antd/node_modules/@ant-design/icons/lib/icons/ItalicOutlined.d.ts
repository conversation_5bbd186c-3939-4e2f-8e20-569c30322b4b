import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![italic](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc5OCAxNjBIMzY2Yy00LjQgMC04IDMuNi04IDh2NjRjMCA0LjQgMy42IDggOCA4aDE4MS4ybC0xNTYgNTQ0SDIyOWMtNC40IDAtOCAzLjYtOCA4djY0YzAgNC40IDMuNiA4IDggOGg0MzJjNC40IDAgOC0zLjYgOC04di02NGMwLTQuNC0zLjYtOC04LThINDc0LjRsMTU2LTU0NEg3OThjNC40IDAgOC0zLjYgOC04di02NGMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
