import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![code-sandbox](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwOS42IDIxMGwuNC0uMmguMkw1MTIgOTYgMzEzLjkgMjA5LjhoLS4ybC43LjNMMTUxLjUgMzA0djQxNkw1MTIgOTI4bDM2MC41LTIwOFYzMDRsLTE2Mi45LTk0ek00ODIuNyA4NDMuNkwzMzkuNiA3NjFWNjIxLjRMMjEwIDU0Ny44VjM3Mi45bDI3Mi43IDE1Ny4zdjMxMy40ek0yMzguMiAzMjEuNWwxMzQuNy03Ny44IDEzOC45IDc5LjcgMTM5LjEtNzkuOSAxMzUuMiA3OC0yNzMuOSAxNTgtMjc0LTE1OHpNODE0IDU0OC4zbC0xMjguOCA3My4xdjEzOS4xbC0xNDMuOSA4M1Y1MzAuNEw4MTQgMzczLjF2MTc1LjJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
