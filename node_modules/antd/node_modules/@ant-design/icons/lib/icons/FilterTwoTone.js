"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FilterTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FilterTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FilterTwoTone = function FilterTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FilterTwoTone.default
  }));
};

/**![filter](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQyMC42IDc5OGgxODIuOVY2NDJINDIwLjZ6TTQxMSA1NjEuNGw5LjUgMTYuNmgxODNsOS41LTE2LjZMODExLjMgMjI2SDIxMi43eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwLjEgMTU0SDE0My45Yy0yNC41IDAtMzkuOCAyNi43LTI3LjUgNDhMMzQ5IDU5Ny40VjgzOGMwIDE3LjcgMTQuMiAzMiAzMS44IDMyaDI2Mi40YzE3LjYgMCAzMS44LTE0LjMgMzEuOC0zMlY1OTcuNEw5MDcuNyAyMDJjMTIuMi0yMS4zLTMuMS00OC0yNy42LTQ4ek02MDMuNSA3OThINDIwLjZWNjQyaDE4Mi45djE1NnptOS41LTIzNi42bC05LjUgMTYuNmgtMTgzbC05LjUtMTYuNkwyMTIuNyAyMjZoNTk4LjZMNjEzIDU2MS40eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FilterTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FilterTwoTone';
}
var _default = exports.default = RefIcon;