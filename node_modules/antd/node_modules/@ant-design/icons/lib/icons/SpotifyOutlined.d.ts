import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![spotify](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTEyIDY0QzI2NC41MiA2NCA2NCAyNjQuNTIgNjQgNTEyczIwMC41MiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjUyIDQ0OC00NDhTNzU5LjQ4IDY0IDUxMiA2NG0wIDc0LjY2YTM3MS44NiAzNzEuODYgMCAwMTI2NC40MyAxMDguOTFBMzcxLjg2IDM3MS44NiAwIDAxODg1LjMzIDUxMmEzNzEuODYgMzcxLjg2IDAgMDEtMTA4LjkgMjY0LjQzQTM3MS44NiAzNzEuODYgMCAwMTUxMiA4ODUuMzNhMzcxLjg2IDM3MS44NiAwIDAxLTI2NC40My0xMDguOUEzNzEuODYgMzcxLjg2IDAgMDExMzguNjcgNTEyYTM3MS44NiAzNzEuODYgMCAwMTEwOC45LTI2NC40M0EzNzEuODYgMzcxLjg2IDAgMDE1MTIgMTM4LjY3TTQ1Mi40OSAzMTZjLTcyLjYxIDAtMTM1LjkgNi43Mi0xOTYgMjUuNjgtMTUuOSAzLjE4LTI5LjE2IDE1LjE2LTI5LjE2IDM3LjM0IDAgMjIuMTQgMTYuMzUgNDEuNyAzOC41IDM4LjQ1IDkuNDggMCAxNS45LTMuNDcgMjIuMTctMy40NyA1MC41OS0xMi43IDEwNy42My0xOC42NyAxNjQuNDktMTguNjcgMTEwLjU1IDAgMjI0IDI0LjY0IDI5OS44MiA2OC44NSA5LjQ5IDMuMiAxMi43IDYuOTggMjIuMTggNi45OCAyMi4xOCAwIDM3LjYzLTE2LjMyIDQwLjg0LTM4LjUgMC0xOC45Ni05LjQ4LTMxLjA2LTIyLjE3LTM3LjMzQzY5OC4zNiAzNDEuNjUgNTcyLjUyIDMxNiA0NTIuNDkgMzE2TTQ0MiA0NTQuODRjLTY2LjM0IDAtMTEzLjYgOS40OS0xNjEuMDIgMjIuMTgtMTUuNzIgNi4yMy0yNC40OSAxNi4wNS0yNC40OSAzNC45OCAwIDE1Ljc2IDEyLjU0IDMxLjUxIDMxLjUxIDMxLjUxIDYuNDIgMCA5LjE4LS4zIDE4LjY3LTMuNTEgMzQuNzItOS40OCA4Mi40LTE1LjE2IDEzMy4wMi0xNS4xNiAxMDQuMjMgMCAxOTQuOTUgMjUuMzkgMjYxLjMzIDY2LjUgNi4yMyAzLjIgMTIuNyA1LjgyIDIyLjE0IDUuODIgMTguOTYgMCAzMS41LTE2LjA2IDMxLjUtMzQuOTggMC0xMi43LTUuOTctMjUuMjQtMTguNjYtMzEuNTEtODIuMTMtNTAuNTktMTg2LjUyLTc1LjgzLTI5NC03NS44M20xMC40OSAxMzYuNWMtNTMuNjUgMC0xMDQuNTMgNS45Ny0xNTUuMTYgMTguNjYtMTIuNjkgMy4yMS0yMi4xNyAxMi4yNC0yMi4xNyAyOCAwIDEyLjcgOS45MyAyNS42OCAyNS42OCAyNS42OCAzLjIxIDAgMTIuNC0zLjUgMTguNjctMy41YTU4MS43MyA1ODEuNzMgMCAwMTEyOS41LTE1LjJjNzguOSAwIDE1MS4wNiAxOC45NyAyMTEuMTcgNTMuNjkgNi40MiAzLjIgMTMuNTUgNS44MiAxOS44MiA1LjgyIDEyLjcgMCAyNC43OS05LjQ4IDI4LTIyLjE0IDAtMTUuOS02Ljg3LTIxLjc2LTE2LjM1LTI4LTY5LjU1LTQxLjE0LTE1MC44LTYzLjAyLTIzOS4xNi02My4wMiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
