import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-image](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU1My4xIDUwOS4xbC03Ny44IDk5LjItNDEuMS01Mi40YTggOCAwIDAwLTEyLjYgMGwtOTkuOCAxMjcuMmE3Ljk4IDcuOTggMCAwMDYuMyAxMi45SDY5NmM2LjcgMCAxMC40LTcuNyA2LjMtMTIuOWwtMTM2LjUtMTc0YTguMSA4LjEgMCAwMC0xMi43IDB6TTM2MCA0NDJhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAwem00OTQuNi0xNTMuNEw2MzkuNCA3My40Yy02LTYtMTQuMS05LjQtMjIuNi05LjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMxMS4zYzAtOC41LTMuNC0xNi43LTkuNC0yMi43ek03OTAuMiAzMjZINjAyVjEzNy44TDc5MC4yIDMyNnptMS44IDU2MkgyMzJWMTM2aDMwMnYyMTZhNDIgNDIgMCAwMDQyIDQyaDIxNnY0OTR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
