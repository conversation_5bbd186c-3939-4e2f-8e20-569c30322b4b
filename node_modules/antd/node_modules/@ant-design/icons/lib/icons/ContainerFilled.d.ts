import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![container](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUyOWMwLS42LjQtMSAxLTFoMjE5LjNsNS4yIDI0LjdDMzk3LjYgNzA4LjUgNDUwLjggNzUyIDUxMiA3NTJzMTE0LjQtNDMuNSAxMjYuNC0xMDMuM2w1LjItMjQuN0g4NjNjLjYgMCAxIC40IDEgMVY5NmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzEyIDQ5M2MwIDQuNC0zLjYgOC04IDhIMzIwYy00LjQgMC04LTMuNi04LTh2LTQ4YzAtNC40IDMuNi04IDgtOGgzODRjNC40IDAgOCAzLjYgOCA4djQ4em0wLTE2MGMwIDQuNC0zLjYgOC04IDhIMzIwYy00LjQgMC04LTMuNi04LTh2LTQ4YzAtNC40IDMuNi04IDgtOGgzODRjNC40IDAgOCAzLjYgOCA4djQ4em0xNTEgMzU0SDY5NC4xYy0xMS42IDMyLjgtMzIgNjIuMy01OS4xIDg0LjctMzQuNSAyOC42LTc4LjIgNDQuMy0xMjMgNDQuM3MtODguNS0xNS44LTEyMy00NC4zYTE5NC4wMiAxOTQuMDIgMCAwMS01OS4xLTg0LjdIMTYxYy0uNiAwLTEtLjQtMS0xdjI0MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNjg2YzAgLjYtLjQgMS0xIDF6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
