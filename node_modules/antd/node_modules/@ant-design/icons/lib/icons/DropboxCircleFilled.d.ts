import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![dropbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY2My44IDQ1NS41em0tMTUxLjUtOTMuOGwtMTUxLjggOTMuOCAxNTEuOCA5My45IDE1MS41LTkzLjl6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNTEuMiA1OTUuNUw1MTIuNiA3NTBsLTE1MS05MC41di0zMy4xbDQ1LjQgMjkuNCAxMDUuNi04Ny43IDEwNS42IDg3LjcgNDUuMS0yOS40djMzLjF6bS00NS42LTIyLjRsLTEwNS4zLTg3LjdMNDA3IDYzNy4xbC0xNTEtOTkuMiAxMDQuNS04Mi40TDI1NiAzNzEuMiA0MDcgMjc0bDEwNS4zIDg3LjdMNjE3LjYgMjc0IDc2OCAzNzIuMWwtMTA0LjIgODMuNUw3NjggNTM5bC0xNTAuNCA5OC4xeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
