import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![hourglass](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc0MiAzMThWMTg0aDg2YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDE5NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg4NnYxMzRjMCA4MS41IDQyLjQgMTUzLjIgMTA2LjQgMTk0LTY0IDQwLjgtMTA2LjQgMTEyLjUtMTA2LjQgMTk0djEzNGgtODZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNjMyYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04aC04NlY3MDZjMC04MS41LTQyLjQtMTUzLjItMTA2LjQtMTk0IDY0LTQwLjggMTA2LjQtMTEyLjUgMTA2LjQtMTk0em0tNzIgMzg4djEzNEgzNTRWNzA2YzAtNDIuMiAxNi40LTgxLjkgNDYuMy0xMTEuN0M0MzAuMSA1NjQuNCA0NjkuOCA1NDggNTEyIDU0OHM4MS45IDE2LjQgMTExLjcgNDYuM0M2NTMuNiA2MjQuMSA2NzAgNjYzLjggNjcwIDcwNnptMC0zODhjMCA0Mi4yLTE2LjQgODEuOS00Ni4zIDExMS43QzU5My45IDQ1OS42IDU1NC4yIDQ3NiA1MTIgNDc2cy04MS45LTE2LjQtMTExLjctNDYuM0ExNTYuNjMgMTU2LjYzIDAgMDEzNTQgMzE4VjE4NGgzMTZ2MTM0eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
