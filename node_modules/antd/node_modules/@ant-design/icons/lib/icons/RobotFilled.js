"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RobotFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RobotFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RobotFilled = function RobotFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RobotFilled.default
  }));
};

/**![robot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NTIgNjRIMTcyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjBjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjgwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMDAgMzI4YzAtMzMuMSAyNi45LTYwIDYwLTYwczYwIDI2LjkgNjAgNjAtMjYuOSA2MC02MCA2MC02MC0yNi45LTYwLTYwem0zNzIgMjQ4YzAgNC40LTMuNiA4LTggOEgzNjBjLTQuNCAwLTgtMy42LTgtOHYtNjBjMC00LjQgMy42LTggOC04aDMwNGM0LjQgMCA4IDMuNiA4IDh2NjB6bS04LTE4OGMtMzMuMSAwLTYwLTI2LjktNjAtNjBzMjYuOS02MCA2MC02MCA2MCAyNi45IDYwIDYwLTI2LjkgNjAtNjAgNjB6bTEzNSA0NzZIMjI1Yy0xMy44IDAtMjUgMTQuMy0yNSAzMnY1NmMwIDQuNCAyLjggOCA2LjIgOGg2MTEuNWMzLjQgMCA2LjItMy42IDYuMi04di01NmMuMS0xNy43LTExLjEtMzItMjQuOS0zMnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(RobotFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RobotFilled';
}
var _default = exports.default = RefIcon;