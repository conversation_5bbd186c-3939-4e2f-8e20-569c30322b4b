import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![linux](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
