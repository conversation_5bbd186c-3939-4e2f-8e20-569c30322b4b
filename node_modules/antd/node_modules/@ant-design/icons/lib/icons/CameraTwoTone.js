"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CameraTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CameraTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CameraTwoTone = function CameraTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CameraTwoTone.default
  }));
};

/**![camera](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAzMjBINjc3LjJsLTE3LjEtNDcuOC0yMi45LTY0LjJIMzg2LjdsLTIyLjkgNjQuMi0xNy4xIDQ3LjhIMTYwYy00LjQgMC04IDMuNi04IDh2NDU2YzAgNC40IDMuNiA4IDggOGg3MDRjNC40IDAgOC0zLjYgOC04VjMyOGMwLTQuNC0zLjYtOC04LTh6TTUxMiA3MDRjLTg4LjQgMC0xNjAtNzEuNi0xNjAtMTYwczcxLjYtMTYwIDE2MC0xNjAgMTYwIDcxLjYgMTYwIDE2MC03MS42IDE2MC0xNjAgMTYweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTEyIDM4NGMtODguNCAwLTE2MCA3MS42LTE2MCAxNjBzNzEuNiAxNjAgMTYwIDE2MCAxNjAtNzEuNiAxNjAtMTYwLTcxLjYtMTYwLTE2MC0xNjB6bTAgMjU2Yy01MyAwLTk2LTQzLTk2LTk2czQzLTk2IDk2LTk2IDk2IDQzIDk2IDk2LTQzIDk2LTk2IDk2eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNODY0IDI0OEg3MjhsLTMyLjQtOTAuOGEzMi4wNyAzMi4wNyAwIDAwLTMwLjItMjEuMkgzNTguNmMtMTMuNSAwLTI1LjYgOC41LTMwLjEgMjEuMkwyOTYgMjQ4SDE2MGMtNDQuMiAwLTgwIDM1LjgtODAgODB2NDU2YzAgNDQuMiAzNS44IDgwIDgwIDgwaDcwNGM0NC4yIDAgODAtMzUuOCA4MC04MFYzMjhjMC00NC4yLTM1LjgtODAtODAtODB6bTggNTM2YzAgNC40LTMuNiA4LTggOEgxNjBjLTQuNCAwLTgtMy42LTgtOFYzMjhjMC00LjQgMy42LTggOC04aDE4Ni43bDE3LjEtNDcuOCAyMi45LTY0LjJoMjUwLjVsMjIuOSA2NC4yIDE3LjEgNDcuOEg4NjRjNC40IDAgOCAzLjYgOCA4djQ1NnoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CameraTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CameraTwoTone';
}
var _default = exports.default = RefIcon;