import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![right-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNTQuNyA0NTQuNWwtMjQ2IDE3OGMtNS4zIDMuOC0xMi43IDAtMTIuNy02LjV2LTQ2LjljMC0xMC4yIDQuOS0xOS45IDEzLjItMjUuOUw1NjYuNiA1MTIgNDIxLjIgNDA2LjhjLTguMy02LTEzLjItMTUuNi0xMy4yLTI1LjlWMzM0YzAtNi41IDcuNC0xMC4zIDEyLjctNi41bDI0NiAxNzhjNC40IDMuMiA0LjQgOS44IDAgMTN6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
