"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _Html5Filled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/Html5Filled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var Html5Filled = function Html5Filled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _Html5Filled.default
  }));
};

/**![html5](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0NS4yIDk2bDY2IDc0Ni42TDUxMiA5MjhsMjk5LjYtODUuNEw4NzguOSA5NkgxNDUuMnptNTk1IDE3Ny4xbC00LjggNDcuMi0xLjcgMTkuNUgzODIuM2w4LjIgOTQuMmgzMzUuMWwtMy4zIDI0LjMtMjEuMiAyNDIuMi0xLjcgMTYuMi0xODcgNTEuNnYuM2gtMS4ybC0uMy4xdi0uMWgtLjFsLTE4OC42LTUyTDMxMC44IDU3Mmg5MS4xbDYuNSA3My4yIDEwMi40IDI3LjdoLjRsMTAyLTI3LjYgMTEuNC0xMTguNkg1MTAuOXYtLjFIMzA2bC0yMi44LTI1My41LTEuNy0yNC4zaDQ2MC4zbC0xLjYgMjQuM3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(Html5Filled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'Html5Filled';
}
var _default = exports.default = RefIcon;