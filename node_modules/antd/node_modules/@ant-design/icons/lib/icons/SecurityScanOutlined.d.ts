import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![security-scan](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnpNNDAyLjkgNTI4LjhsLTc3LjUgNzcuNWE4LjAzIDguMDMgMCAwMDAgMTEuM2wzNCAzNGMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDc3LjUtNzcuNWM1NS43IDM1LjEgMTMwLjEgMjguNCAxNzguNi0yMC4xIDU2LjMtNTYuMyA1Ni4zLTE0Ny41IDAtMjAzLjgtNTYuMy01Ni4zLTE0Ny41LTU2LjMtMjAzLjggMC00OC41IDQ4LjUtNTUuMiAxMjMtMjAuMSAxNzguNnptNjUuNC0xMzMuM2MzMS4zLTMxLjMgODItMzEuMyAxMTMuMiAwIDMxLjMgMzEuMyAzMS4zIDgyIDAgMTEzLjItMzEuMyAzMS4zLTgyIDMxLjMtMTEzLjIgMHMtMzEuMy04MS45IDAtMTEzLjJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
