"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _SkinOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/SkinOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var SkinOutlined = function SkinOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _SkinOutlined.default
  }));
};

/**![skin](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg3MCAxMjZINjYzLjhjLTE3LjQgMC0zMi45IDExLjktMzcgMjkuM0M2MTQuMyAyMDguMSA1NjcgMjQ2IDUxMiAyNDZzLTEwMi4zLTM3LjktMTE0LjgtOTAuN2EzNy45MyAzNy45MyAwIDAwLTM3LTI5LjNIMTU0YTQ0IDQ0IDAgMDAtNDQgNDR2MjUyYTQ0IDQ0IDAgMDA0NCA0NGg3NXYzODhhNDQgNDQgMCAwMDQ0IDQ0aDQ3OGE0NCA0NCAwIDAwNDQtNDRWNDY2aDc1YTQ0IDQ0IDAgMDA0NC00NFYxNzBhNDQgNDQgMCAwMC00NC00NHptLTI4IDI2OEg3MjN2NDMySDMwMVYzOTRIMTgyVjE5OGgxNTMuM2MyOC4yIDcxLjIgOTcuNSAxMjAgMTc2LjcgMTIwczE0OC41LTQ4LjggMTc2LjctMTIwSDg0MnYxOTZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SkinOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SkinOutlined';
}
var _default = exports.default = RefIcon;