"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FireFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FireFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FireFilled = function FireFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FireFilled.default
  }));
};

/**![fire](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzNC4xIDQ2OS4yQTM0Ny40OSAzNDcuNDkgMCAwMDc1MS4yIDM1NGwtMjkuMS0yNi43YTguMDkgOC4wOSAwIDAwLTEzIDMuM2wtMTMgMzcuM2MtOC4xIDIzLjQtMjMgNDcuMy00NC4xIDcwLjgtMS40IDEuNS0zIDEuOS00LjEgMi0xLjEuMS0yLjgtLjEtNC4zLTEuNS0xLjQtMS4yLTIuMS0zLTItNC44IDMuNy02MC4yLTE0LjMtMTI4LjEtNTMuNy0yMDJDNTU1LjMgMTcxIDUxMCAxMjMuMSA0NTMuNCA4OS43bC00MS4zLTI0LjNjLTUuNC0zLjItMTIuMyAxLTEyIDcuM2wyLjIgNDhjMS41IDMyLjgtMi4zIDYxLjgtMTEuMyA4NS45LTExIDI5LjUtMjYuOCA1Ni45LTQ3IDgxLjVhMjk1LjY0IDI5NS42NCAwIDAxLTQ3LjUgNDYuMSAzNTIuNiAzNTIuNiAwIDAwLTEwMC4zIDEyMS41QTM0Ny43NSAzNDcuNzUgMCAwMDE2MCA2MTBjMCA0Ny4yIDkuMyA5Mi45IDI3LjcgMTM2YTM0OS40IDM0OS40IDAgMDA3NS41IDExMC45YzMyLjQgMzIgNzAgNTcuMiAxMTEuOSA3NC43QzQxOC41IDk0OS44IDQ2NC41IDk1OSA1MTIgOTU5czkzLjUtOS4yIDEzNi45LTI3LjNBMzQ4LjYgMzQ4LjYgMCAwMDc2MC44IDg1N2MzMi40LTMyIDU3LjgtNjkuNCA3NS41LTExMC45YTM0NC4yIDM0NC4yIDAgMDAyNy43LTEzNmMwLTQ4LjgtMTAtOTYuMi0yOS45LTE0MC45eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FireFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FireFilled';
}
var _default = exports.default = RefIcon;