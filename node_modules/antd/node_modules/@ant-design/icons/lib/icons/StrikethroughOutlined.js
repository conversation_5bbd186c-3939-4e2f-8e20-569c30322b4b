"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _StrikethroughOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/StrikethroughOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var StrikethroughOutlined = function StrikethroughOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _StrikethroughOutlined.default
  }));
};

/**![strikethrough](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiA0NzRINTY5LjljLTEwLTItMjAuNS00LTMxLjYtNi0xNS45LTIuOS0yMi4yLTQuMS0zMC44LTUuOC01MS4zLTEwLTgyLjItMjAtMTA2LjgtMzQuMi0zNS4xLTIwLjUtNTIuMi00OC4zLTUyLjItODUuMSAwLTM3IDE1LjItNjcuNyA0NC04OSAyOC40LTIxIDY4LjgtMzIuMSAxMTYuOC0zMi4xIDU0LjggMCA5Ny4xIDE0LjQgMTI1LjggNDIuOCAxNC42IDE0LjQgMjUuMyAzMi4xIDMxLjggNTIuNiAxLjMgNC4xIDIuOCAxMCA0LjMgMTcuOC45IDQuOCA1LjIgOC4yIDkuOSA4LjJoNzIuOGM1LjYgMCAxMC4xLTQuNiAxMC4xLTEwLjF2LTFjLS43LTYuOC0xLjMtMTIuMS0yLTE2LTcuMy00My41LTI4LTgxLjctNTkuNy0xMTAuMy00NC40LTQwLjUtMTA5LjctNjEuOC0xODguNy02MS44LTcyLjMgMC0xMzcuNCAxOC4xLTE4My4zIDUwLjktMjUuNiAxOC40LTQ1LjQgNDEuMi01OC42IDY3LjctMTMuNSAyNy4xLTIwLjMgNTguNC0yMC4zIDkyLjkgMCAyOS41IDUuNyA1NC41IDE3LjMgNzYuNSA4LjMgMTUuNyAxOS42IDI5LjUgMzQuMSA0Mkg3MmMtNC40IDAtOCAzLjYtOCA4djYwYzAgNC40IDMuNiA4IDggOGg0MzMuMmMyLjEuNCAzLjkuOCA1LjkgMS4yIDMwLjkgNi4yIDQ5LjUgMTAuNCA2Ni42IDE1LjIgMjMgNi41IDQwLjYgMTMuMyA1NS4yIDIxLjUgMzUuOCAyMC4yIDUzLjMgNDkuMiA1My4zIDg5IDAgMzUuMy0xNS41IDY2LjgtNDMuNiA4OC44LTMwLjUgMjMuOS03NS42IDM2LjQtMTMwLjUgMzYuNC00My43IDAtODAuNy04LjUtMTEwLjItMjUtMjkuMS0xNi4zLTQ5LjEtMzkuOC01OS43LTY5LjUtLjgtMi4yLTEuNy01LjItMi43LTktMS4yLTQuNC01LjMtNy41LTkuNy03LjVoLTc5LjdjLTUuNiAwLTEwLjEgNC42LTEwLjEgMTAuMXYxYy4yIDIuMy40IDQuMi42IDUuNyA2LjUgNDguOCAzMC4zIDg4LjggNzAuNyAxMTguOCA0Ny4xIDM0LjggMTEzLjQgNTMuMiAxOTEuOCA1My4yIDg0LjIgMCAxNTQuOC0xOS44IDIwNC4yLTU3LjMgMjUtMTguOSA0NC4yLTQyLjIgNTcuMS02OSAxMy0yNy4xIDE5LjctNTcuOSAxOS43LTkxLjUgMC0zMS44LTUuOC01OC40LTE3LjgtODEuNC01LjgtMTEuMi0xMy4xLTIxLjUtMjEuOC0zMC44SDk1MmM0LjQgMCA4LTMuNiA4LTh2LTYwYTggOCAwIDAwLTgtNy45eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(StrikethroughOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StrikethroughOutlined';
}
var _default = exports.default = RefIcon;