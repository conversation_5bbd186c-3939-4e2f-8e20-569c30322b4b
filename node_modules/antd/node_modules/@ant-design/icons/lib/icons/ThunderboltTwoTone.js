"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ThunderboltTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ThunderboltTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ThunderboltTwoTone = function ThunderboltTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ThunderboltTwoTone.default
  }));
};

/**![thunderbolt](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NS40IDE2NC4xSDQ3MC44TDI4MS4yIDQ5MS41aDE1Ny40bC02MC4zIDI0MSAzMTkuOC0zMDUuMWgtMjExeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODQ4LjEgMzU5LjNINjI3LjhMODI1LjkgMTA5YzQuMS01LjMuNC0xMy02LjMtMTNINDM2LjFjLTIuOCAwLTUuNSAxLjUtNi45IDRMMTcwLjEgNTQ3LjVjLTMuMSA1LjMuNyAxMiA2LjkgMTJoMTc0LjRMMjYyIDkxNy4xYy0xLjkgNy44IDcuNSAxMy4zIDEzLjMgNy43TDg1My42IDM3M2M1LjItNC45IDEuNy0xMy43LTUuNS0xMy43ek0zNzguMyA3MzIuNWw2MC4zLTI0MUgyODEuMmwxODkuNi0zMjcuNGgyMjQuNkw0ODcuMSA0MjcuNGgyMTFMMzc4LjMgNzMyLjV6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ThunderboltTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ThunderboltTwoTone';
}
var _default = exports.default = RefIcon;