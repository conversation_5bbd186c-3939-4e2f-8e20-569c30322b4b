"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _HddTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/HddTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var HddTwoTone = function HddTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _HddTwoTone.default
  }));
};

/**![hdd](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIzMiA4ODhoNTYwVjY4MEgyMzJ2MjA4em00NDgtMTQwYzIyLjEgMCA0MCAxNy45IDQwIDQwcy0xNy45IDQwLTQwIDQwLTQwLTE3LjktNDAtNDAgMTcuOS00MCA0MC00MHpNMjMyIDYxNmg1NjBWNDA4SDIzMnYyMDh6bTcyLTEyOGMwLTQuNCAzLjYtOCA4LThoMTg0YzQuNCAwIDggMy42IDggOHY0OGMwIDQuNC0zLjYgOC04IDhIMzEyYy00LjQgMC04LTMuNi04LTh2LTQ4em0tNzItMTQ0aDU2MFYxMzZIMjMydjIwOHptNzItMTI4YzAtNC40IDMuNi04IDgtOGgxODRjNC40IDAgOCAzLjYgOCA4djQ4YzAgNC40LTMuNiA4LTggOEgzMTJjLTQuNCAwLTgtMy42LTgtOHYtNDh6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04MzIgNjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgODI0SDIzMlY2ODBoNTYwdjIwOHptMC0yNzJIMjMyVjQwOGg1NjB2MjA4em0wLTI3MkgyMzJWMTM2aDU2MHYyMDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0zMTIgNTQ0aDE4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgzMTJjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDh6bTAtMjcyaDE4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOEgzMTJjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDh6bTMyOCA1MTZhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(HddTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HddTwoTone';
}
var _default = exports.default = RefIcon;