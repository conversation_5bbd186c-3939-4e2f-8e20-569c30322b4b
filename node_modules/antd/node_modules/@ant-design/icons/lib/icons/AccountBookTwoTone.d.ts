import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![account-book](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
