"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _AlertFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/AlertFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var AlertFilled = function AlertFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _AlertFilled.default
  }));
};

/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAyNDRjMTc2LjE4IDAgMzE5IDE0Mi44MiAzMTkgMzE5djIzM2EzMiAzMiAwIDAxLTMyIDMySDIyNWEzMiAzMiAwIDAxLTMyLTMyVjU2M2MwLTE3Ni4xOCAxNDIuODItMzE5IDMxOS0zMTl6TTQ4NCA2OGg1NmE4IDggMCAwMTggOHY5NmE4IDggMCAwMS04IDhoLTU2YTggOCAwIDAxLTgtOFY3NmE4IDggMCAwMTgtOHpNMTc3LjI1IDE5MS42NmE4IDggMCAwMTExLjMyIDBsNjcuODggNjcuODhhOCA4IDAgMDEwIDExLjMxbC0zOS42IDM5LjZhOCA4IDAgMDEtMTEuMzEgMGwtNjcuODgtNjcuODhhOCA4IDAgMDEwLTExLjMxbDM5LjYtMzkuNnptNjY5LjYgMGwzOS42IDM5LjZhOCA4IDAgMDEwIDExLjNsLTY3Ljg4IDY3LjlhOCA4IDAgMDEtMTEuMzIgMGwtMzkuNi0zOS42YTggOCAwIDAxMC0xMS4zMmw2Ny44OS02Ny44OGE4IDggMCAwMTExLjMxIDB6TTE5MiA4OTJoNjQwYTMyIDMyIDAgMDEzMiAzMnYyNGE4IDggMCAwMS04IDhIMTY4YTggOCAwIDAxLTgtOHYtMjRhMzIgMzIgMCAwMTMyLTMyem0xNDgtMzE3djI1M2g2NFY1NzVoLTY0eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(AlertFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AlertFilled';
}
var _default = exports.default = RefIcon;