import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM3Mi41IDI1NkgxODR2NTEyaDY1NlYzNzAuNEg0OTIuMUwzNzIuNSAyNTZ6TTU0MCA0NDMuMVY1MjhoODQuNWM0LjEgMCA3LjUgMy4xIDcuNSA3djQyYzAgMy44LTMuNCA3LTcuNSA3SDU0MHY4NC45YzAgMy45LTMuMSA3LjEtNyA3LjFoLTQyYy0zLjggMC03LTMuMi03LTcuMVY1ODRoLTg0LjVjLTQuMSAwLTcuNS0zLjItNy41LTd2LTQyYzAtMy45IDMuNC03IDcuNS03SDQ4NHYtODQuOWMwLTMuOSAzLjItNy4xIDctNy4xaDQyYzMuOSAwIDcgMy4yIDcgNy4xeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwIDI5OC40SDUyMUw0MDMuNyAxODYuMmE4LjE1IDguMTUgMCAwMC01LjUtMi4ySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NTkyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYzMzAuNGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNODQwIDc2OEgxODRWMjU2aDE4OC41bDExOS42IDExNC40SDg0MFY3Njh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik00ODQgNDQzLjFWNTI4aC04NC41Yy00LjEgMC03LjUgMy4xLTcuNSA3djQyYzAgMy44IDMuNCA3IDcuNSA3SDQ4NHY4NC45YzAgMy45IDMuMiA3LjEgNyA3LjFoNDJjMy45IDAgNy0zLjIgNy03LjFWNTg0aDg0LjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjktMy40LTctNy41LTdINTQwdi04NC45YzAtMy45LTMuMS03LjEtNy03LjFoLTQyYy0zLjggMC03IDMuMi03IDcuMXoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
