import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![caret-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OSAxNjUuMUwzMDguMiA0OTMuNWMtMTAuOSA5LjQtMTAuOSAyNy41IDAgMzdMNjg5IDg1OC45YzE0LjIgMTIuMiAzNSAxLjIgMzUtMTguNVYxODMuNmMwLTE5LjctMjAuOC0zMC43LTM1LTE4LjV6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
