import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![fast-forward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc5My44IDQ5OS4zTDUwNi40IDI3My41Yy0xMC43LTguNC0yNi40LS44LTI2LjQgMTIuN3Y0NTEuNmMwIDEzLjUgMTUuNyAyMS4xIDI2LjQgMTIuN2wyODcuNC0yMjUuOGExNi4xNCAxNi4xNCAwIDAwMC0yNS40em0tMzIwIDBMMTg2LjQgMjczLjVjLTEwLjctOC40LTI2LjQtLjgtMjYuNCAxMi43djQ1MS41YzAgMTMuNSAxNS43IDIxLjEgMjYuNCAxMi43bDI4Ny40LTIyNS44YzQuMS0zLjIgNi4yLTggNi4yLTEyLjcgMC00LjYtMi4xLTkuNC02LjItMTIuNnpNODU3LjYgMjQ4aC01MS4yYy0zLjUgMC02LjQgMi43LTYuNCA2djUxNmMwIDMuMyAyLjkgNiA2LjQgNmg1MS4yYzMuNSAwIDYuNC0yLjcgNi40LTZWMjU0YzAtMy4zLTIuOS02LTYuNC02eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
