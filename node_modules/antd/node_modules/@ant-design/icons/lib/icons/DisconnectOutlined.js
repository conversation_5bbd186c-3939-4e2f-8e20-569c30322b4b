"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DisconnectOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DisconnectOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DisconnectOutlined = function DisconnectOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DisconnectOutlined.default
  }));
};

/**![disconnect](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMi42IDE5MS40Yy04NC42LTg0LjYtMjIxLjUtODQuNi0zMDYgMGwtOTYuOSA5Ni45IDUxIDUxIDk2LjktOTYuOWM1My44LTUzLjggMTQ0LjYtNTkuNSAyMDQgMCA1OS41IDU5LjUgNTMuOCAxNTAuMiAwIDIwNGwtOTYuOSA5Ni45IDUxLjEgNTEuMSA5Ni45LTk2LjljODQuNC04NC42IDg0LjQtMjIxLjUtLjEtMzA2LjF6TTQ0Ni41IDc4MS42Yy01My44IDUzLjgtMTQ0LjYgNTkuNS0yMDQgMC01OS41LTU5LjUtNTMuOC0xNTAuMiAwLTIwNGw5Ni45LTk2LjktNTEuMS01MS4xLTk2LjkgOTYuOWMtODQuNiA4NC42LTg0LjYgMjIxLjUgMCAzMDZzMjIxLjUgODQuNiAzMDYgMGw5Ni45LTk2LjktNTEtNTEtOTYuOCA5N3pNMjYwLjMgMjA5LjRhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDIwOS40IDI0OWE4LjAzIDguMDMgMCAwMDAgMTEuM2w1NTQuNCA1NTQuNGMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDM5LjYtMzkuNmMzLjEtMy4xIDMuMS04LjIgMC0xMS4zTDI2MC4zIDIwOS40eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(DisconnectOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DisconnectOutlined';
}
var _default = exports.default = RefIcon;