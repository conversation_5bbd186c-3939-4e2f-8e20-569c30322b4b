"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DropboxOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DropboxOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DropboxOutlined = function DropboxOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DropboxOutlined.default
  }));
};

/**![dropbox](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY0IDU1Ni45bDI2NC4yIDE3My41TDUxMi41IDU3NyAyNDYuOCA0MTIuN3ptODk2LTI5MC4zem0wIDBMNjk2LjggOTUgNTEyLjUgMjQ4LjVsMjY1LjIgMTY0LjJMNTEyLjUgNTc3bDE4NC4zIDE1My40TDk2MCA1NTguOCA3NzcuNyA0MTIuN3pNNTEzIDYwOS44TDMyOC4yIDc2My4zbC03OS40LTUxLjV2NTcuOEw1MTMgOTI4bDI2My43LTE1OC40di01Ny44bC03OC45IDUxLjV6TTMyOC4yIDk1TDY0IDI2NS4xbDE4Mi44IDE0Ny42IDI2NS43LTE2NC4yek02NCA1NTYuOXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(DropboxOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DropboxOutlined';
}
var _default = exports.default = RefIcon;