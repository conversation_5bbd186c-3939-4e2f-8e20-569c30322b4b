"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FileUnknownTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FileUnknownTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FileUnknownTwoTone = function FileUnknownTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FileUnknownTwoTone.default
  }));
};

/**![file-unknown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptLTIyIDQyNGMtMTcuNyAwLTMyLTE0LjMtMzItMzJzMTQuMy0zMiAzMi0zMiAzMiAxNC4zIDMyIDMyLTE0LjMgMzItMzIgMzJ6bTExMC0yMjguNGMuNyA0NC45LTI5LjcgODQuNS03NC4zIDk4LjktNS43IDEuOC05LjcgNy4zLTkuNyAxMy4zVjY3MmMwIDUuNS00LjUgMTAtMTAgMTBoLTMyYy01LjUgMC0xMC00LjUtMTAtMTB2LTMyYy4yLTE5LjggMTUuNC0zNy4zIDM0LjctNDAuMUM1NDkgNTk2LjIgNTcwIDU3NC4zIDU3MCA1NDljMC0yOC4xLTI1LjgtNTEuNS01OC01MS41cy01OCAyMy40LTU4IDUxLjZjMCA1LjItNC40IDkuNC05LjggOS40aC0zMi40Yy01LjQgMC05LjgtNC4xLTkuOC05LjUgMC01Ny40IDUwLjEtMTAzLjcgMTExLjUtMTAzIDU5LjMuOCAxMDcuNyA0Ni4xIDEwOC41IDEwMS42eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjdMNjM5LjQgNzMuNGMtNi02LTE0LjItOS40LTIyLjctOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNi05LjQtMjIuNnpNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmgzMDJ2MjE2YTQyIDQyIDAgMDA0MiA0MmgyMTZ2NDk0eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNDgwIDc0NGEzMiAzMiAwIDEwNjQgMCAzMiAzMiAwIDEwLTY0IDB6bS03OC0xOTVjMCA1LjQgNC40IDkuNSA5LjggOS41aDMyLjRjNS40IDAgOS44LTQuMiA5LjgtOS40IDAtMjguMiAyNS44LTUxLjYgNTgtNTEuNnM1OCAyMy40IDU4IDUxLjVjMCAyNS4zLTIxIDQ3LjItNDkuMyA1MC45LTE5LjMgMi44LTM0LjUgMjAuMy0zNC43IDQwLjF2MzJjMCA1LjUgNC41IDEwIDEwIDEwaDMyYzUuNSAwIDEwLTQuNSAxMC0xMHYtMTIuMmMwLTYgNC0xMS41IDkuNy0xMy4zIDQ0LjYtMTQuNCA3NS01NCA3NC4zLTk4LjktLjgtNTUuNS00OS4yLTEwMC44LTEwOC41LTEwMS42LTYxLjQtLjctMTExLjUgNDUuNi0xMTEuNSAxMDN6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FileUnknownTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FileUnknownTwoTone';
}
var _default = exports.default = RefIcon;