import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![security-scan](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNNjI2LjggNTU0Yy00OC41IDQ4LjUtMTIzIDU1LjItMTc4LjYgMjAuMWwtNzcuNSA3Ny41YTguMDMgOC4wMyAwIDAxLTExLjMgMGwtMzQtMzRhOC4wMyA4LjAzIDAgMDEwLTExLjNsNzcuNS03Ny41Yy0zNS4xLTU1LjctMjguNC0xMzAuMSAyMC4xLTE3OC42IDU2LjMtNTYuMyAxNDcuNS01Ni4zIDIwMy44IDAgNTYuMyA1Ni4zIDU2LjMgMTQ3LjUgMCAyMDMuOHptLTE1OC41NC00NS4yN2E4MC4xIDgwLjEgMCAxMDExMy4yNy0xMTMuMjggODAuMSA4MC4xIDAgMTAtMTEzLjI3IDExMy4yOHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
