import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![windows](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyMC4xIDc3MC42TDQ0MyA4MjMuMlY1NDMuOEgxMjAuMXYyMjYuOHptNjMuNC0xNjMuNWgxOTYuMnYxNDEuNmwtMTk2LjItMzEuOVY2MDcuMXptMzQwLjMgMjI2LjVsMzgyIDYyLjJ2LTM1MmgtMzgydjI4OS44em02My40LTIyNi41aDI1NS4zdjIxNC40bC0yNTUuMy00MS42VjYwNy4xem0tNjMuNC00MTUuN3YyODguOGgzODJWMTI4LjFsLTM4MiA2My4zem0zMTguNyAyMjUuNUg1ODcuM1YyNDVsMjU1LjMtNDIuM3YyMTQuMnptLTcyMi40IDYzLjNINDQzVjIwMS45bC0zMjIuOSA1My41djIyNC44ek0xODMuNSAzMDlsMTk2LjItMzIuNXYxNDAuNEgxODMuNVYzMDl6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
