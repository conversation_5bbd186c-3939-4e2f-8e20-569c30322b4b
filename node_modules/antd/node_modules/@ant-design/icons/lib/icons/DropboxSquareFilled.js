"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _DropboxSquareFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/DropboxSquareFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var DropboxSquareFilled = function DropboxSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _DropboxSquareFilled.default
  }));
};

/**![dropbox-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjYzLjIgNjU5LjVMNTEyLjYgNzUwbC0xNTEtOTAuNXYtMzMuMWw0NS40IDI5LjQgMTA1LjYtODcuNyAxMDUuNiA4Ny43IDQ1LjEtMjkuNHYzMy4xem0tNDUuNi0yMi40bC0xMDUuMy04Ny43TDQwNyA2MzcuMWwtMTUxLTk5LjIgMTA0LjUtODIuNEwyNTYgMzcxLjIgNDA3IDI3NGwxMDUuMyA4Ny43TDYxNy42IDI3NCA3NjggMzcyLjFsLTEwNC4yIDgzLjVMNzY4IDUzOWwtMTUwLjQgOTguMXpNNTEyLjMgMzYxLjdsLTE1MS44IDkzLjggMTUxLjggOTMuOSAxNTEuNS05My45em0xNTEuNSA5My44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(DropboxSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'DropboxSquareFilled';
}
var _default = exports.default = RefIcon;