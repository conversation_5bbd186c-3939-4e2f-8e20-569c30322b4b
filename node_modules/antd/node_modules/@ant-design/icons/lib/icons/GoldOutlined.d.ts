import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![gold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0MiA0NzJoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDM4Mi4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOHptOTEuMi0xOTZoMTU5LjVsMjAuNyAxMjhoLTIwMWwyMC44LTEyOHptMi41IDI4Mi43Yy0uNi0zLjktNC02LjctNy45LTYuN0gxNjYuMmMtMy45IDAtNy4zIDIuOC03LjkgNi43bC00MC4yIDI0OGMtLjEuNC0uMS45LS4xIDEuMyAwIDQuNCAzLjYgOCA4IDhoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDh6TTE5Ni41IDc0OGwyMC43LTEyOGgxNTkuNWwyMC43IDEyOEgxOTYuNXptNzA5LjQgNTguN2wtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDU5Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjMtLjcgNy4zLTQuOCA2LjYtOS4yek02MjYuNSA3NDhsMjAuNy0xMjhoMTU5LjVsMjAuNyAxMjhINjI2LjV6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
