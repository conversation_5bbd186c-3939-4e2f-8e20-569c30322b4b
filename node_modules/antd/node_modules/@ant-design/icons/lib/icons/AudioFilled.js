"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _AudioFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/AudioFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var AudioFilled = function AudioFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _AudioFilled.default
  }));
};

/**![audio](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2MjRjOTMuOSAwIDE3MC03NS4yIDE3MC0xNjhWMjMyYzAtOTIuOC03Ni4xLTE2OC0xNzAtMTY4cy0xNzAgNzUuMi0xNzAgMTY4djIyNGMwIDkyLjggNzYuMSAxNjggMTcwIDE2OHptMzMwLTE3MGMwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDggMCAxNDAuMy0xMTMuNyAyNTQtMjU0IDI1NFMyNTggNTk0LjMgMjU4IDQ1NGMwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDggMCAxNjguNyAxMjYuNiAzMDcuOSAyOTAgMzI3LjZWODg0SDMyNi43Yy0xMy43IDAtMjQuNyAxNC4zLTI0LjcgMzJ2MzZjMCA0LjQgMi44IDggNi4yIDhoNDA3LjZjMy40IDAgNi4yLTMuNiA2LjItOHYtMzZjMC0xNy43LTExLTMyLTI0LjctMzJINTQ4Vjc4Mi4xYzE2NS4zLTE4IDI5NC0xNTggMjk0LTMyOC4xeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(AudioFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AudioFilled';
}
var _default = exports.default = RefIcon;