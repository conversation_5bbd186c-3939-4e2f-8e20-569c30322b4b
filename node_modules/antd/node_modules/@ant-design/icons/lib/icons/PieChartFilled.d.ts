import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pie-chart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2My4xIDUxOC41SDUwNS41VjE2MC45YzAtNC40LTMuNi04LTgtOGgtMjZhMzk4LjU3IDM5OC41NyAwIDAwLTI4Mi41IDExNyAzOTcuNDcgMzk3LjQ3IDAgMDAtODUuNiAxMjdDODIuNiA0NDYuMiA3MiA0OTguNSA3MiA1NTIuNVM4Mi42IDY1OC43IDEwMy40IDcwOGMyMC4xIDQ3LjUgNDguOSA5MC4zIDg1LjYgMTI3IDM2LjcgMzYuNyA3OS40IDY1LjUgMTI3IDg1LjZhMzk2LjY0IDM5Ni42NCAwIDAwMTU1LjYgMzEuNSAzOTguNTcgMzk4LjU3IDAgMDAyODIuNS0xMTdjMzYuNy0zNi43IDY1LjUtNzkuNCA4NS42LTEyN2EzOTYuNjQgMzk2LjY0IDAgMDAzMS41LTE1NS42di0yNmMtLjEtNC40LTMuNy04LTguMS04ek05NTEgNDYzbC0yLjYtMjguMmMtOC41LTkyLTQ5LjMtMTc4LjgtMTE1LjEtMjQ0LjNBMzk4LjUgMzk4LjUgMCAwMDU4OC40IDc1LjZMNTYwLjEgNzNjLTQuNy0uNC04LjcgMy4yLTguNyA3Ljl2MzgzLjdjMCA0LjQgMy42IDggOCA4bDM4My42LTFjNC43LS4xIDguNC00IDgtOC42eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
