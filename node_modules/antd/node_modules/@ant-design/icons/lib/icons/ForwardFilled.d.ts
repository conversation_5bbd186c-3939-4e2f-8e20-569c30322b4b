import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![forward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgyNS44IDQ5OEw1MzguNCAyNDkuOWMtMTAuNy05LjItMjYuNC0uOS0yNi40IDE0djQ5Ni4zYzAgMTQuOSAxNS43IDIzLjIgMjYuNCAxNEw4MjUuOCA1MjZjOC4zLTcuMiA4LjMtMjAuOCAwLTI4em0tMzIwIDBMMjE4LjQgMjQ5LjljLTEwLjctOS4yLTI2LjQtLjktMjYuNCAxNHY0OTYuM2MwIDE0LjkgMTUuNyAyMy4yIDI2LjQgMTRMNTA1LjggNTI2YzQuMS0zLjYgNi4yLTguOCA2LjItMTQgMC01LjItMi4xLTEwLjQtNi4yLTE0eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
