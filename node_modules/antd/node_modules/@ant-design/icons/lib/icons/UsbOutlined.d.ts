import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![usb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc2MCA0MzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDI5NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2Mjg4Yy02Ni4yIDAtMTIwIDUyLjEtMTIwIDExNnYzNTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFY1NDhjMC0yNC4zIDIxLjYtNDQgNDguMS00NGg0OTUuOGMyNi41IDAgNDguMSAxOS43IDQ4LjEgNDR2MzU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWNTQ4YzAtNjMuOS01My44LTExNi0xMjAtMTE2em0tNDI0IDBWMTg0aDM1MnYyNDhIMzM2em0xMjAtMTg0aC00OGMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGg0OGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptMTYwIDBoLTQ4Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDQ4YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
