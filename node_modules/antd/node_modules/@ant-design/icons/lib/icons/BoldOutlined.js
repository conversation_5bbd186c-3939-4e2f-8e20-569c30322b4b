"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _BoldOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/BoldOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var BoldOutlined = function BoldOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _BoldOutlined.default
  }));
};

/**![bold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5Ny44IDQ4MS40YzMzLjYtMzUgNTQuMi04Mi4zIDU0LjItMTM0LjN2LTEwLjJDNzUyIDIyOS4zIDY2My45IDE0MiA1NTUuMyAxNDJIMjU5LjRjLTE1LjEgMC0yNy40IDEyLjMtMjcuNCAyNy40djY3OS4xYzAgMTYuMyAxMy4yIDI5LjUgMjkuNSAyOS41aDMxOC43YzExNyAwIDIxMS44LTk0LjIgMjExLjgtMjEwLjV2LTExYzAtNzMtMzcuNC0xMzcuMy05NC4yLTE3NS4xek0zMjggMjM4aDIyNC43YzU3LjEgMCAxMDMuMyA0NC40IDEwMy4zIDk5LjN2OS41YzAgNTQuOC00Ni4zIDk5LjMtMTAzLjMgOTkuM0gzMjhWMjM4em0zNjYuNiA0MjkuNGMwIDYyLjktNTEuNyAxMTMuOS0xMTUuNSAxMTMuOUgzMjhWNTQyLjdoMjUxLjFjNjMuOCAwIDExNS41IDUxIDExNS41IDExMy45djEwLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BoldOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BoldOutlined';
}
var _default = exports.default = RefIcon;