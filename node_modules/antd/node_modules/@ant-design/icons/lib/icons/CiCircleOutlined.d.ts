import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![ci-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptMjE4LTU3Mi4xaC01MC40Yy00LjQgMC04IDMuNi04IDh2Mzg0LjJjMCA0LjQgMy42IDggOCA4SDczMGM0LjQgMCA4LTMuNiA4LThWMzE5LjljMC00LjQtMy42LTgtOC04em0tMjgxLjQgNDkuNmM0OS41IDAgODMuMSAzMS41IDg3IDc3LjYuNCA0LjIgMy44IDcuNCA4IDcuNGg1Mi42YzIuNCAwIDQuNC0yIDQuNC00LjQgMC04MS4yLTY0LTEzOC4xLTE1Mi4zLTEzOC4xQzM0NS40IDMwNCAyODYgMzczLjUgMjg2IDQ4OC40djQ5YzAgMTE0IDU5LjQgMTgyLjYgMTYyLjMgMTgyLjYgODggMCAxNTIuMy01NS4xIDE1Mi4zLTEzMi41IDAtMi40LTItNC40LTQuNC00LjRoLTUyLjdjLTQuMiAwLTcuNiAzLjItOCA3LjMtNC4yIDQzLTM3LjcgNzIuNC04NyA3Mi40LTYxLjEgMC05NS42LTQ0LjktOTUuNi0xMjUuMnYtNDkuM2MuMS04MS40IDM0LjYtMTI2LjggOTUuNy0xMjYuOHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
