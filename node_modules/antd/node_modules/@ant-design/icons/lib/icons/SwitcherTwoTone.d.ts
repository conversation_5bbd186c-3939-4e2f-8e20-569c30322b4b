import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![switcher](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4NCA4NDBoNTI4VjMxMkgxODR2NTI4em0xMTYtMjkwaDI5NnY2NEgzMDB2LTY0eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwIDExMkgyNjRjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNTc2djU3NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTc1MiAyNDBIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2MDhjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjA4YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjI3MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDYwMEgxODRWMzEyaDUyOHY1Mjh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0zMDAgNTUwaDI5NnY2NEgzMDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
