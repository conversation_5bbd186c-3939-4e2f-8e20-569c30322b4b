"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FormatPainterOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FormatPainterOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FormatPainterOutlined = function FormatPainterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FormatPainterOutlined.default
  }));
};

/**![format-painter](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NDAgMTkyaC01NnYtNzJjMC0xMy4zLTEwLjctMjQtMjQtMjRIMTY4Yy0xMy4zIDAtMjQgMTAuNy0yNCAyNHYyNzJjMCAxMy4zIDEwLjcgMjQgMjQgMjRoNTkyYzEzLjMgMCAyNC0xMC43IDI0LTI0VjI1NmgzMnYyMDBINDY1Yy0yMi4xIDAtNDAgMTcuOS00MCA0MHYxMzZoLTQ0Yy00LjQgMC04IDMuNi04IDh2MjI4YzAgLjYuMSAxLjMuMiAxLjlBODMuOTkgODMuOTkgMCAwMDQ1NyA5NjBjNDYuNCAwIDg0LTM3LjYgODQtODQgMC0yLjEtLjEtNC4xLS4yLTYuMS4xLS42LjItMS4yLjItMS45VjY0MGMwLTQuNC0zLjYtOC04LThoLTQ0VjUyMGgzNTFjMjIuMSAwIDQwLTE3LjkgNDAtNDBWMjMyYzAtMjIuMS0xNy45LTQwLTQwLTQwek03MjAgMzUySDIwOFYxNjBoNTEydjE5MnpNNDc3IDg3NmMwIDExLTkgMjAtMjAgMjBzLTIwLTktMjAtMjBWNjk2aDQwdjE4MHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FormatPainterOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FormatPainterOutlined';
}
var _default = exports.default = RefIcon;