import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![insert-row-right](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NTYgMTEyaC04MGMtNC40IDAtOCAzLjYtOCA4djc4NGMwIDQuNCAzLjYgOCA4IDhoODBjNC40IDAgOC0zLjYgOC04VjEyMGMwLTQuNC0zLjYtOC04LTh6bS0yMDAgMEgxOTJjLTE3LjcgMC0zMiAxNC45LTMyIDMzLjN2NzMzLjNjMCAxOC40IDE0LjMgMzMuMyAzMiAzMy4zaDQ2NGMxNy43IDAgMzItMTQuOSAzMi0zMy4zVjE0NS4zYzAtMTguNC0xNC4zLTMzLjMtMzItMzMuM3pNMzkyIDg0MEgyMzJWNjY0aDE2MHYxNzZ6bTAtMjQwSDIzMlY0MjRoMTYwdjE3NnptMC0yNDBIMjMyVjE4NGgxNjB2MTc2em0yMjQgNDgwSDQ1NlY2NjRoMTYwdjE3NnptMC0yNDBINDU2VjQyNGgxNjB2MTc2em0wLTI0MEg0NTZWMTg0aDE2MHYxNzZ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
