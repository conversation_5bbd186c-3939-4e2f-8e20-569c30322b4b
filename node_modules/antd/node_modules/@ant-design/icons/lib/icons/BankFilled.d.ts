import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![bank](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5NCA0NjJjMzAuOSAwIDQzLjgtMzkuNyAxOC43LTU4TDUzMC44IDEyNi4yYTMxLjgxIDMxLjgxIDAgMDAtMzcuNiAwTDExMS4zIDQwNGMtMjUuMSAxOC4yLTEyLjIgNTggMTguOCA1OEgxOTJ2Mzc0aC03MmMtNC40IDAtOCAzLjYtOCA4djUyYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01MmMwLTQuNC0zLjYtOC04LThoLTcyVjQ2Mmg2MnpNMzgxIDgzNkgyNjRWNDYyaDExN3YzNzR6bTE4OSAwSDQ1M1Y0NjJoMTE3djM3NHptMTkwIDBINjQyVjQ2MmgxMTh2Mzc0eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
