"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ExpandOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ExpandOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ExpandOutlined = function ExpandOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ExpandOutlined.default
  }));
};

/**![expand](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zNDIgODhIMTIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyMjRjMCA4LjggNy4yIDE2IDE2IDE2aDQ4YzguOCAwIDE2LTcuMiAxNi0xNlYxNjhoMTc0YzguOCAwIDE2LTcuMiAxNi0xNnYtNDhjMC04LjgtNy4yLTE2LTE2LTE2em01NzggNTc2aC00OGMtOC44IDAtMTYgNy4yLTE2IDE2djE3Nkg2ODJjLTguOCAwLTE2IDcuMi0xNiAxNnY0OGMwIDguOCA3LjIgMTYgMTYgMTZoMjIyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjY4MGMwLTguOC03LjItMTYtMTYtMTZ6TTM0MiA4NTZIMTY4VjY4MGMwLTguOC03LjItMTYtMTYtMTZoLTQ4Yy04LjggMC0xNiA3LjItMTYgMTZ2MjI0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDIyMmM4LjggMCAxNi03LjIgMTYtMTZ2LTQ4YzAtOC44LTcuMi0xNi0xNi0xNnpNOTA0IDg4SDY4MmMtOC44IDAtMTYgNy4yLTE2IDE2djQ4YzAgOC44IDcuMiAxNiAxNiAxNmgxNzR2MTc2YzAgOC44IDcuMiAxNiAxNiAxNmg0OGM4LjggMCAxNi03LjIgMTYtMTZWMTIwYzAtMTcuNy0xNC4zLTMyLTMyLTMyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ExpandOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ExpandOutlined';
}
var _default = exports.default = RefIcon;