import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![yahoo](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkzNy4zIDIzMUg4MjQuN2MtMTUuNSAwLTI3LjcgMTIuNi0yNy4xIDI4LjFsMTMuMSAzNjZoODQuNGw2NS40LTM2Ni40YzIuNy0xNS4yLTcuOC0yNy43LTIzLjItMjcuN3ptLTc3LjQgNDUwLjRoLTE0LjFjLTI3LjEgMC00OS4yIDIyLjItNDkuMiA0OS4zdjE0LjFjMCAyNy4xIDIyLjIgNDkuMyA0OS4yIDQ5LjNoMTQuMWMyNy4xIDAgNDkuMi0yMi4yIDQ5LjItNDkuM3YtMTQuMWMwLTI3LjEtMjIuMi00OS4zLTQ5LjItNDkuM3pNNDAyLjYgMjMxQzIxNi4yIDIzMSA2NSAzNTcgNjUgNTEyLjVTMjE2LjIgNzk0IDQwMi42IDc5NHMzMzcuNi0xMjYgMzM3LjYtMjgxLjVTNTg5LjEgMjMxIDQwMi42IDIzMXptMjI1LjIgMjI1LjJoLTY1LjNMNDU4LjkgNTU5Ljh2NjUuM2g4NC40djU2LjNIMzE4LjJ2LTU2LjNoODQuNHYtNjUuM0wyNDIuOSAzOTkuOWgtMzd2LTU2LjNoMTY4LjV2NTYuM2gtMzdsOTMuNCA5My41IDI4LjEtMjguMVY0MDBoMTY4Ljh2NTYuMnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
