import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![ci-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bS02My41IDUyMi44YzQ5LjMgMCA4Mi44LTI5LjQgODctNzIuNC40LTQuMSAzLjgtNy4zIDgtNy4zaDUyLjdjMi40IDAgNC40IDIgNC40IDQuNCAwIDc3LjQtNjQuMyAxMzIuNS0xNTIuMyAxMzIuNUMzNDUuNCA3MjAgMjg2IDY1MS40IDI4NiA1MzcuNHYtNDlDMjg2IDM3My41IDM0NS40IDMwNCA0NDguMyAzMDRjODguMyAwIDE1Mi4zIDU2LjkgMTUyLjMgMTM4LjEgMCAyLjQtMiA0LjQtNC40IDQuNGgtNTIuNmMtNC4yIDAtNy42LTMuMi04LTcuNC0zLjktNDYuMS0zNy41LTc3LjYtODctNzcuNi02MS4xIDAtOTUuNiA0NS40LTk1LjcgMTI2Ljh2NDkuM2MwIDgwLjMgMzQuNSAxMjUuMiA5NS42IDEyNS4yek03MzggNzA0LjFjMCA0LjQtMy42IDgtOCA4aC01MC40Yy00LjQgMC04LTMuNi04LThWMzE5LjljMC00LjQgMy42LTggOC04SDczMGM0LjQgMCA4IDMuNiA4IDh2Mzg0LjJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik03MzAgMzExLjloLTUwLjRjLTQuNCAwLTggMy42LTggOHYzODQuMmMwIDQuNCAzLjYgOCA4IDhINzMwYzQuNCAwIDgtMy42IDgtOFYzMTkuOWMwLTQuNC0zLjYtOC04LTh6bS0yODEuNCA0OS42YzQ5LjUgMCA4My4xIDMxLjUgODcgNzcuNi40IDQuMiAzLjggNy40IDggNy40aDUyLjZjMi40IDAgNC40LTIgNC40LTQuNCAwLTgxLjItNjQtMTM4LjEtMTUyLjMtMTM4LjFDMzQ1LjQgMzA0IDI4NiAzNzMuNSAyODYgNDg4LjR2NDljMCAxMTQgNTkuNCAxODIuNiAxNjIuMyAxODIuNiA4OCAwIDE1Mi4zLTU1LjEgMTUyLjMtMTMyLjUgMC0yLjQtMi00LjQtNC40LTQuNGgtNTIuN2MtNC4yIDAtNy42IDMuMi04IDcuMy00LjIgNDMtMzcuNyA3Mi40LTg3IDcyLjQtNjEuMSAwLTk1LjYtNDQuOS05NS42LTEyNS4ydi00OS4zYy4xLTgxLjQgMzQuNi0xMjYuOCA5NS43LTEyNi44eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
