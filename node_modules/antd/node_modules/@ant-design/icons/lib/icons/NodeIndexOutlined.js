"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _NodeIndexOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/NodeIndexOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var NodeIndexOutlined = function NodeIndexOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _NodeIndexOutlined.default
  }));
};

/**![node-index](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NDMuNSA3MzcuNGMtMTIuNC03NS4yLTc5LjItMTI5LjEtMTU1LjMtMTI1LjRTNTUwLjkgNjc2IDU0NiA3NTJjLTE1My41LTQuOC0yMDgtNDAuNy0xOTkuMS0xMTMuNyAzLjMtMjcuMyAxOS44LTQxLjkgNTAuMS00OSAxOC40LTQuMyAzOC44LTQuOSA1Ny4zLTMuMiAxLjcuMiAzLjUuMyA1LjIuNSAxMS4zIDIuNyAyMi44IDUgMzQuMyA2LjggMzQuMSA1LjYgNjguOCA4LjQgMTAxLjggNi42IDkyLjgtNSAxNTYtNDUuOSAxNTkuMi0xMzIuNyAzLjEtODQuMS01NC43LTE0My43LTE0Ny45LTE4My42LTI5LjktMTIuOC02MS42LTIyLjctOTMuMy0zMC4yLTE0LjMtMy40LTI2LjMtNS43LTM1LjItNy4yLTcuOS03NS45LTcxLjUtMTMzLjgtMTQ3LjgtMTM0LjQtNzYuMy0uNi0xNDAuOSA1Ni4xLTE1MC4xIDEzMS45czQwIDE0Ni4zIDExNC4yIDE2My45Yzc0LjIgMTcuNiAxNDkuOS0yMy4zIDE3NS43LTk1LjEgOS40IDEuNyAxOC43IDMuNiAyOCA1LjggMjguMiA2LjYgNTYuNCAxNS40IDgyLjQgMjYuNiA3MC43IDMwLjIgMTA5LjMgNzAuMSAxMDcuNSAxMTkuOS0xLjYgNDQuNi0zMy42IDY1LjItOTYuMiA2OC42LTI3LjUgMS41LTU3LjYtLjktODcuMy01LjgtOC4zLTEuNC0xNS45LTIuOC0yMi42LTQuMy0zLjktLjgtNi42LTEuNS03LjgtMS44bC0zLjEtLjZjLTIuMi0uMy01LjktLjgtMTAuNy0xLjMtMjUtMi4zLTUyLjEtMS41LTc4LjUgNC42LTU1LjIgMTIuOS05My45IDQ3LjItMTAxLjEgMTA1LjgtMTUuNyAxMjYuMiA3OC42IDE4NC43IDI3NiAxODguOSAyOS4xIDcwLjQgMTA2LjQgMTA3LjkgMTc5LjYgODcgNzMuMy0yMC45IDExOS4zLTkzLjQgMTA2LjktMTY4LjZ6TTMyOS4xIDM0NS4yYTgzLjMgODMuMyAwIDExLjAxLTE2Ni42MSA4My4zIDgzLjMgMCAwMS0uMDEgMTY2LjYxek02OTUuNiA4NDVhODMuMyA4My4zIDAgMTEuMDEtMTY2LjYxQTgzLjMgODMuMyAwIDAxNjk1LjYgODQ1eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(NodeIndexOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'NodeIndexOutlined';
}
var _default = exports.default = RefIcon;