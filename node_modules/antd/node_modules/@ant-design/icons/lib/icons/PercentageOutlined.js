"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _PercentageOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/PercentageOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var PercentageOutlined = function PercentageOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _PercentageOutlined.default
  }));
};

/**![percentage](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NS43IDIxMC44bC00Mi40LTQyLjRhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDE2OC4zIDgwMS45YTguMDMgOC4wMyAwIDAwMCAxMS4zbDQyLjQgNDIuNGMzLjEgMy4xIDguMiAzLjEgMTEuMyAwTDg1NS42IDIyMmMzLjItMyAzLjItOC4xLjEtMTEuMnpNMzA0IDQ0OGM3OS40IDAgMTQ0LTY0LjYgMTQ0LTE0NHMtNjQuNi0xNDQtMTQ0LTE0NC0xNDQgNjQuNi0xNDQgMTQ0IDY0LjYgMTQ0IDE0NCAxNDR6bTAtMjE2YzM5LjcgMCA3MiAzMi4zIDcyIDcycy0zMi4zIDcyLTcyIDcyLTcyLTMyLjMtNzItNzIgMzIuMy03MiA3Mi03MnptNDE2IDM0NGMtNzkuNCAwLTE0NCA2NC42LTE0NCAxNDRzNjQuNiAxNDQgMTQ0IDE0NCAxNDQtNjQuNiAxNDQtMTQ0LTY0LjYtMTQ0LTE0NC0xNDR6bTAgMjE2Yy0zOS43IDAtNzItMzIuMy03Mi03MnMzMi4zLTcyIDcyLTcyIDcyIDMyLjMgNzIgNzItMzIuMyA3Mi03MiA3MnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PercentageOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PercentageOutlined';
}
var _default = exports.default = RefIcon;