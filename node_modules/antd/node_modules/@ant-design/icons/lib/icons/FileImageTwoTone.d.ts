import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![file-image](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptLTEzNCA1MGMyMi4xIDAgNDAgMTcuOSA0MCA0MHMtMTcuOSA0MC00MCA0MC00MC0xNy45LTQwLTQwIDE3LjktNDAgNDAtNDB6bTI5NiAyOTRIMzI4LjFjLTYuNyAwLTEwLjQtNy43LTYuMy0xMi45bDk5LjgtMTI3LjJhOCA4IDAgMDExMi42IDBsNDEuMSA1Mi40IDc3LjgtOTkuMmE4LjEgOC4xIDAgMDExMi43IDBsMTM2LjUgMTc0YzQuMSA1LjIuNCAxMi45LTYuMyAxMi45eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjZMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmgzMDJ2MjE2YTQyIDQyIDAgMDA0MiA0MmgyMTZ2NDk0eiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNTUzLjEgNTA5LjFsLTc3LjggOTkuMi00MS4xLTUyLjRhOCA4IDAgMDAtMTIuNiAwbC05OS44IDEyNy4yYTcuOTggNy45OCAwIDAwNi4zIDEyLjlINjk2YzYuNyAwIDEwLjQtNy43IDYuMy0xMi45bC0xMzYuNS0xNzRhOC4xIDguMSAwIDAwLTEyLjcgMHpNMzYwIDQ0MmE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
