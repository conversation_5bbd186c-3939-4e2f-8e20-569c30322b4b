"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CrownTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CrownTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CrownTwoTone = function CrownTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CrownTwoTone.default
  }));
};

/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMS45IDI4My45di41TDgzNS41IDg2NWMtMSA4LTcuOSAxNC0xNS45IDE0SDIwNC41Yy04LjEgMC0xNC45LTYuMS0xNi0xNGwtNzYuNC01ODAuNnYtLjYgMS42TDE4OC41IDg2NmMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuMS0uNS4xLTEgMC0xLjV6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik03NzMuNiA4MTAuNmw1My45LTQwOS40LTEzOS44IDg2LjFMNTEyIDI1Mi45IDMzNi4zIDQ4Ny4zbC0xMzkuOC04Ni4xIDUzLjggNDA5LjRoNTIzLjN6bS0zNzQuMi0xODljMC02Mi4xIDUwLjUtMTEyLjYgMTEyLjYtMTEyLjZzMTEyLjYgNTAuNSAxMTIuNiAxMTIuNnYxYzAgNjIuMS01MC41IDExMi42LTExMi42IDExMi42cy0xMTIuNi01MC41LTExMi42LTExMi42di0xeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTEyIDczNC4yYzYxLjkgMCAxMTIuMy01MC4yIDExMi42LTExMi4xdi0uNWMwLTYyLjEtNTAuNS0xMTIuNi0xMTIuNi0xMTIuNnMtMTEyLjYgNTAuNS0xMTIuNiAxMTIuNnYuNWMuMyA2MS45IDUwLjcgMTEyLjEgMTEyLjYgMTEyLjF6bTAtMTYwLjljMjYuNiAwIDQ4LjIgMjEuNiA0OC4yIDQ4LjMgMCAyNi42LTIxLjYgNDguMy00OC4yIDQ4LjNzLTQ4LjItMjEuNi00OC4yLTQ4LjNjMC0yNi42IDIxLjYtNDguMyA0OC4yLTQ4LjN6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODguNSA4NjVjMS4xIDcuOSA3LjkgMTQgMTYgMTRoNjE1LjFjOCAwIDE0LjktNiAxNS45LTE0bDc2LjQtNTgwLjZ2LS41Yy4zLTYuNC02LjctMTAuOC0xMi4zLTcuNEw3MDUgMzk2LjQgNTE4LjQgMTQ3LjVhOC4wNiA4LjA2IDAgMDAtMTIuOSAwTDMxOSAzOTYuNCAxMjQuMyAyNzYuNWMtNS41LTMuNC0xMi42LjktMTIuMiA3LjN2LjZMMTg4LjUgODY1em0xNDcuOC0zNzcuN0w1MTIgMjUyLjlsMTc1LjcgMjM0LjQgMTM5LjgtODYuMS01My45IDQwOS40SDI1MC4zbC01My44LTQwOS40IDEzOS44IDg2LjF6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CrownTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CrownTwoTone';
}
var _default = exports.default = RefIcon;