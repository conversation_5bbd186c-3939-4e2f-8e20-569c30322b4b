import TimePickerLocale from '../../time-picker/locale/sl_SI';
// Merge into a locale object
const locale = {
  lang: {
    locale: 'sl',
    placeholder: 'Izberite datum',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON><PERSON> datum', 'Kon<PERSON><PERSON> datum'],
    today: 'Dane<PERSON>',
    now: 'Trenutno',
    backToToday: 'Nazaj na trenutni datum',
    ok: 'OK',
    clear: '<PERSON><PERSON><PERSON><PERSON>',
    week: '<PERSON><PERSON>',
    month: 'Mesec',
    year: 'Leto',
    timeSelect: 'Izberi čas',
    dateSelect: 'Izberi datum',
    monthSelect: 'Izberite mesec',
    yearSelect: 'Izberite leto',
    decadeSelect: 'Izberite desetletje',
    yearFormat: 'YYYY',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    previousMonth: 'Prejš<PERSON> mesec (PageUp)',
    nextMonth: 'Nasle<PERSON>ji mesec (PageDown)',
    previousYear: '<PERSON><PERSON><PERSON> leto (Control + left)',
    nextYear: 'Naslednje leto (Control + right)',
    previousDecade: 'Prejšnje desetletje',
    nextDecade: 'Naslednje desetletje',
    previousCentury: 'Zadnje stoletje',
    nextCentury: 'Naslednje stoletje'
  },
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;