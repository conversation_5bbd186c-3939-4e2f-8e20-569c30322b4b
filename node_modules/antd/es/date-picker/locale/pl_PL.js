import CalendarLocale from "rc-picker/es/locale/pl_PL";
import TimePickerLocale from '../../time-picker/locale/pl_PL';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON><PERSON><PERSON><PERSON> datę',
    rangePlaceholder: ['Data początkowa', 'Data końcowa'],
    yearFormat: 'YYYY',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    shortWeekDays: ['Niedz', 'Pon', 'Wt', 'Śr', 'Czw', 'Pt', 'Sob'],
    shortMonths: ['Sty', 'Lut', '<PERSON>', 'Kwi', 'Maj', '<PERSON><PERSON>', 'Lip', '<PERSON>e', 'Wrz', '<PERSON><PERSON>', 'Lis', 'Gru']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;