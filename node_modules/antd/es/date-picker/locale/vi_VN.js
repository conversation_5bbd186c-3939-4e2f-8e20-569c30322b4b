import CalendarLocale from "rc-picker/es/locale/vi_VN";
import TimePickerLocale from '../../time-picker/locale/vi_VN';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON>ọ<PERSON> thời điểm',
    yearPlaceholder: '<PERSON>ọ<PERSON> năm',
    quarterPlaceholder: '<PERSON>ọn quý',
    monthPlaceholder: '<PERSON>ọ<PERSON> tháng',
    weekPlaceholder: 'Chọn tuần',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeMonthPlaceholder: ['Th<PERSON>g bắt đầu', '<PERSON>h<PERSON><PERSON> kết thúc'],
    rangeWeekPlaceholder: ['Tuần bắt đầu', 'Tuần kết thúc']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;