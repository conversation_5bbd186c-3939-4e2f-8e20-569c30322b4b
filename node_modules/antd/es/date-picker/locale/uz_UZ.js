import CalendarLocale from "rc-picker/es/locale/uz_UZ";
import TimePickerLocale from '../../time-picker/locale/uz_UZ';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: 'Sanani tanlang',
    yearPlaceholder: '<PERSON><PERSON><PERSON> tanlang',
    quarterPlaceholder: '<PERSON><PERSON><PERSON> tanlang',
    monthPlaceholder: '<PERSON><PERSON><PERSON> tanlang',
    weekPlaceholder: 'Haftani tanlang',
    rangePlaceholder: ['Boshlanish sanasi', 'Tugallanish sanasi'],
    rangeYearPlaceholder: ['Boshlanish yili', 'Tugallanish yili'],
    rangeMonthPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> oyi', 'Tugallanish oyi'],
    rangeWeekPlaceholder: ['<PERSON><PERSON><PERSON><PERSON> haftasi', 'Tugallanish haftasi']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;