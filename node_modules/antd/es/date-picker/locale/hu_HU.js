import CalendarLocale from "rc-picker/es/locale/hu_HU";
import TimePickerLocale from '../../time-picker/locale/hu_HU';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON><PERSON>lass<PERSON> dátumot',
    rangePlaceholder: ['<PERSON><PERSON><PERSON><PERSON> dátum', '<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;