import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  Tabs,
  Space,
  message,
  Divider,
  Alert,
  Upload,
  Modal,
  Table,
  Tag,
  Popconfirm,
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface SystemConfig {
  // 基本设置
  system_name: string;
  system_description: string;
  admin_email: string;
  timezone: string;
  language: string;
  
  // 监控设置
  monitoring_enabled: boolean;
  monitoring_interval: number;
  alert_enabled: boolean;
  alert_email: string;
  alert_threshold_cpu: number;
  alert_threshold_memory: number;
  alert_threshold_disk: number;
  
  // 安全设置
  session_timeout: number;
  max_login_attempts: number;
  password_min_length: number;
  require_strong_password: boolean;
  enable_two_factor: boolean;
  
  // 日志设置
  log_level: string;
  log_retention_days: number;
  enable_audit_log: boolean;
  
  // 备份设置
  backup_enabled: boolean;
  backup_interval: number;
  backup_retention_days: number;
  backup_path: string;
}

interface BackupRecord {
  id: number;
  filename: string;
  size: number;
  created_at: string;
  type: string;
  status: string;
}

const SystemSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<SystemConfig>({
    system_name: '设备管理系统',
    system_description: '企业级设备管理与监控平台',
    admin_email: '<EMAIL>',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    
    monitoring_enabled: true,
    monitoring_interval: 60,
    alert_enabled: true,
    alert_email: '<EMAIL>',
    alert_threshold_cpu: 80,
    alert_threshold_memory: 85,
    alert_threshold_disk: 90,
    
    session_timeout: 30,
    max_login_attempts: 5,
    password_min_length: 8,
    require_strong_password: true,
    enable_two_factor: false,
    
    log_level: 'INFO',
    log_retention_days: 30,
    enable_audit_log: true,
    
    backup_enabled: true,
    backup_interval: 24,
    backup_retention_days: 7,
    backup_path: '/var/backups/device-manager',
  });

  const [backups, setBackups] = useState<BackupRecord[]>([
    {
      id: 1,
      filename: 'backup_20240101_120000.tar.gz',
      size: 1024 * 1024 * 50, // 50MB
      created_at: '2024-01-01 12:00:00',
      type: 'auto',
      status: 'completed'
    },
    {
      id: 2,
      filename: 'backup_20240102_120000.tar.gz',
      size: 1024 * 1024 * 52,
      created_at: '2024-01-02 12:00:00',
      type: 'auto',
      status: 'completed'
    }
  ]);

  useEffect(() => {
    loadConfig();
    loadBackups();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      // 这里应该调用API获取配置
      // const response = await settingsApi.getConfig();
      // setConfig(response.data);
      form.setFieldsValue(config);
    } catch (error) {
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const loadBackups = async () => {
    try {
      // 这里应该调用API获取备份列表
      // const response = await settingsApi.getBackups();
      // setBackups(response.data);
    } catch (error) {
      message.error('加载备份列表失败');
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 这里应该调用API保存配置
      // await settingsApi.updateConfig(values);
      
      setConfig({ ...config, ...values });
      message.success('配置保存成功');
    } catch (error) {
      message.error('配置保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(config);
    message.info('配置已重置');
  };

  const handleCreateBackup = async () => {
    try {
      setLoading(true);
      // 这里应该调用API创建备份
      // await settingsApi.createBackup();
      
      const newBackup: BackupRecord = {
        id: Date.now(),
        filename: `backup_${new Date().toISOString().replace(/[:.]/g, '-')}.tar.gz`,
        size: Math.floor(Math.random() * 100) * 1024 * 1024,
        created_at: new Date().toLocaleString(),
        type: 'manual',
        status: 'completed'
      };
      
      setBackups([newBackup, ...backups]);
      message.success('备份创建成功');
    } catch (error) {
      message.error('备份创建失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadBackup = (backup: BackupRecord) => {
    // 这里应该调用API下载备份
    message.success(`开始下载备份: ${backup.filename}`);
  };

  const handleDeleteBackup = async (id: number) => {
    try {
      // 这里应该调用API删除备份
      // await settingsApi.deleteBackup(id);
      
      setBackups(backups.filter(b => b.id !== id));
      message.success('备份删除成功');
    } catch (error) {
      message.error('备份删除失败');
    }
  };

  const handleExportConfig = () => {
    const configJson = JSON.stringify(config, null, 2);
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'system-config.json';
    link.click();
    URL.revokeObjectURL(url);
    message.success('配置导出成功');
  };

  const handleImportConfig = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedConfig = JSON.parse(e.target?.result as string);
        setConfig(importedConfig);
        form.setFieldsValue(importedConfig);
        message.success('配置导入成功');
      } catch (error) {
        message.error('配置文件格式错误');
      }
    };
    reader.readAsText(file);
    return false;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const backupColumns: ColumnsType<BackupRecord> = [
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      render: (size) => formatFileSize(size),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'auto' ? 'blue' : 'green'}>
          {type === 'auto' ? '自动' : '手动'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'completed' ? 'success' : 'processing'}>
          {status === 'completed' ? '完成' : '进行中'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => handleDownloadBackup(record)}
          >
            下载
          </Button>
          <Popconfirm
            title="确定要删除这个备份吗？"
            onConfirm={() => handleDeleteBackup(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="系统设置"
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadConfig}>
              重新加载
            </Button>
            <Button icon={<DownloadOutlined />} onClick={handleExportConfig}>
              导出配置
            </Button>
            <Upload
              beforeUpload={handleImportConfig}
              showUploadList={false}
              accept=".json"
            >
              <Button icon={<UploadOutlined />}>导入配置</Button>
            </Upload>
            <Button onClick={handleReset}>重置</Button>
            <Button type="primary" icon={<SaveOutlined />} onClick={handleSave} loading={loading}>
              保存配置
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical" initialValues={config}>
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本设置" key="basic">
              <Form.Item name="system_name" label="系统名称">
                <Input placeholder="请输入系统名称" />
              </Form.Item>
              <Form.Item name="system_description" label="系统描述">
                <TextArea rows={3} placeholder="请输入系统描述" />
              </Form.Item>
              <Form.Item name="admin_email" label="管理员邮箱">
                <Input type="email" placeholder="请输入管理员邮箱" />
              </Form.Item>
              <Form.Item name="timezone" label="时区">
                <Select>
                  <Option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</Option>
                  <Option value="UTC">UTC (UTC+0)</Option>
                  <Option value="America/New_York">America/New_York (UTC-5)</Option>
                </Select>
              </Form.Item>
              <Form.Item name="language" label="语言">
                <Select>
                  <Option value="zh-CN">简体中文</Option>
                  <Option value="en-US">English</Option>
                </Select>
              </Form.Item>
            </TabPane>

            <TabPane tab="监控设置" key="monitoring">
              <Form.Item name="monitoring_enabled" label="启用监控" valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name="monitoring_interval" label="监控间隔(秒)">
                <InputNumber min={10} max={3600} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="alert_enabled" label="启用告警" valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name="alert_email" label="告警邮箱">
                <Input type="email" placeholder="请输入告警邮箱" />
              </Form.Item>
              <Divider>告警阈值</Divider>
              <Form.Item name="alert_threshold_cpu" label="CPU使用率阈值(%)">
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="alert_threshold_memory" label="内存使用率阈值(%)">
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="alert_threshold_disk" label="磁盘使用率阈值(%)">
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </TabPane>

            <TabPane tab="安全设置" key="security">
              <Form.Item name="session_timeout" label="会话超时(分钟)">
                <InputNumber min={5} max={1440} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="max_login_attempts" label="最大登录尝试次数">
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="password_min_length" label="密码最小长度">
                <InputNumber min={6} max={32} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="require_strong_password" label="要求强密码" valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name="enable_two_factor" label="启用双因子认证" valuePropName="checked">
                <Switch />
              </Form.Item>
            </TabPane>

            <TabPane tab="日志设置" key="logging">
              <Form.Item name="log_level" label="日志级别">
                <Select>
                  <Option value="DEBUG">DEBUG</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="WARNING">WARNING</Option>
                  <Option value="ERROR">ERROR</Option>
                </Select>
              </Form.Item>
              <Form.Item name="log_retention_days" label="日志保留天数">
                <InputNumber min={1} max={365} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="enable_audit_log" label="启用审计日志" valuePropName="checked">
                <Switch />
              </Form.Item>
            </TabPane>

            <TabPane tab="备份设置" key="backup">
              <Form.Item name="backup_enabled" label="启用自动备份" valuePropName="checked">
                <Switch />
              </Form.Item>
              <Form.Item name="backup_interval" label="备份间隔(小时)">
                <InputNumber min={1} max={168} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="backup_retention_days" label="备份保留天数">
                <InputNumber min={1} max={365} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item name="backup_path" label="备份路径">
                <Input placeholder="请输入备份路径" />
              </Form.Item>
              
              <Divider>备份管理</Divider>
              <Space style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateBackup}
                  loading={loading}
                >
                  创建备份
                </Button>
                <Button icon={<ReloadOutlined />} onClick={loadBackups}>
                  刷新列表
                </Button>
              </Space>
              
              <Table
                columns={backupColumns}
                dataSource={backups}
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </div>
  );
};

export default SystemSettings;
