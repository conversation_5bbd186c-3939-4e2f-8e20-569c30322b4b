import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Tabs,
  Upload,
  Typography,
} from 'antd';
import { scriptApi, serverApi } from '../../services/api';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  UploadOutlined,
  DownloadOutlined,
  FileTextOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

interface Script {
  id: number;
  name: string;
  description: string;
  script_type: string;
  category: string;
  tags: string[];
  content: string;
  created_at: string;
  updated_at: string;
}

interface ExecutionRecord {
  id: number;
  script_name: string;
  server_name: string;
  status: 'success' | 'failed' | 'running';
  exit_code: number;
  duration: number;
  started_at: string;
  finished_at: string;
}

const ScriptManagement: React.FC = () => {
  const [scripts, setScripts] = useState<Script[]>([]);
  const [executions, setExecutions] = useState<ExecutionRecord[]>([]);
  const [servers, setServers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [executeModalVisible, setExecuteModalVisible] = useState(false);
  const [editingScript, setEditingScript] = useState<Script | null>(null);
  const [selectedScript, setSelectedScript] = useState<Script | null>(null);
  const [form] = Form.useForm();
  const [executeForm] = Form.useForm();

  // 加载数据
  const loadScripts = async () => {
    try {
      setLoading(true);
      const response = await scriptApi.getScripts();
      setScripts(response.data || []);
    } catch (error) {
      console.error('加载脚本列表失败:', error);
      message.error('加载脚本列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadExecutions = async () => {
    try {
      const response = await scriptApi.getExecutions();
      setExecutions(response.data || []);
    } catch (error) {
      console.error('加载执行记录失败:', error);
    }
  };

  const loadServers = async () => {
    try {
      const response = await serverApi.getServers();
      setServers(response.data || []);
    } catch (error) {
      console.error('加载服务器列表失败:', error);
    }
  };

  useEffect(() => {
    loadScripts();
    loadExecutions();
    loadServers();
  }, []);

  const scriptColumns: ColumnsType<Script> = [
    {
      title: '脚本名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'script_type',
      key: 'script_type',
      render: (text) => {
        const colorMap: { [key: string]: string } = {
          shell: 'blue',
          python: 'green',
          javascript: 'orange',
        };
        return <Tag color={colorMap[text] || 'default'}>{text}</Tag>;
      },
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (text) => <Tag>{text}</Tag>,
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <>
          {tags?.map((tag) => (
            <Tag key={tag} color="blue">
              {tag}
            </Tag>
          ))}
        </>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<FileTextOutlined />}
            onClick={() => handleViewScript(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<PlayCircleOutlined />}
            onClick={() => handleExecuteScript(record)}
          >
            执行
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditScript(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个脚本吗？"
            onConfirm={() => handleDeleteScript(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const executionColumns: ColumnsType<ExecutionRecord> = [
    {
      title: '脚本名称',
      dataIndex: 'script_name',
      key: 'script_name',
    },
    {
      title: '服务器',
      dataIndex: 'server_name',
      key: 'server_name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: { [key: string]: string } = {
          success: 'green',
          failed: 'red',
          running: 'blue',
        };
        const textMap: { [key: string]: string } = {
          success: '成功',
          failed: '失败',
          running: '运行中',
        };
        return <Tag color={colorMap[status]}>{textMap[status]}</Tag>;
      },
    },
    {
      title: '退出码',
      dataIndex: 'exit_code',
      key: 'exit_code',
    },
    {
      title: '耗时(秒)',
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: '开始时间',
      dataIndex: 'started_at',
      key: 'started_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleViewExecutionLogs(record)}>
            查看日志
          </Button>
        </Space>
      ),
    },
  ];

  const handleAddScript = () => {
    setEditingScript(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditScript = (script: Script) => {
    setEditingScript(script);
    form.setFieldsValue(script);
    setModalVisible(true);
  };

  const handleViewScript = (script: Script) => {
    Modal.info({
      title: `脚本内容 - ${script.name}`,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>描述：</Text> {script.description}
          </div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>类型：</Text> <Tag color="blue">{script.script_type}</Tag>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>内容：</Text>
          </div>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: 16, 
            borderRadius: 4,
            maxHeight: 400,
            overflow: 'auto'
          }}>
            {script.content}
          </pre>
        </div>
      ),
      width: 800,
    });
  };

  const handleExecuteScript = (script: Script) => {
    setSelectedScript(script);
    executeForm.resetFields();
    setExecuteModalVisible(true);
  };

  const handleDeleteScript = async (id: number) => {
    try {
      await scriptApi.deleteScript(id);
      message.success('脚本删除成功');
      loadScripts(); // 重新加载列表
    } catch (error) {
      message.error('脚本删除失败');
    }
  };

  const handleViewExecutionLogs = (record: ExecutionRecord) => {
    Modal.info({
      title: `执行日志 - ${record.script_name}`,
      content: (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>服务器：</Text> {record.server_name}
          </div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>状态：</Text> 
            <Tag color={record.status === 'success' ? 'green' : 'red'}>
              {record.status === 'success' ? '成功' : '失败'}
            </Tag>
          </div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>日志输出：</Text>
          </div>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: 16, 
            borderRadius: 4,
            maxHeight: 400,
            overflow: 'auto'
          }}>
            {record.status === 'success' 
              ? '脚本执行成功\n输出结果...' 
              : '脚本执行失败\n错误信息...'}
          </pre>
        </div>
      ),
      width: 800,
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingScript) {
        await scriptApi.updateScript(editingScript.id, values);
        message.success('脚本更新成功');
      } else {
        await scriptApi.createScript(values);
        message.success('脚本创建成功');
      }

      setModalVisible(false);
      form.resetFields();
      loadScripts(); // 重新加载列表
    } catch (error) {
      console.error('操作失败:', error);
      message.error(editingScript ? '脚本更新失败' : '脚本创建失败');
    }
  };

  const handleExecuteOk = async () => {
    try {
      const values = await executeForm.validateFields();

      if (!selectedScript) {
        message.error('请选择要执行的脚本');
        return;
      }

      // 执行脚本
      await scriptApi.executeScript(selectedScript.id, {
        server_id: values.server,
        parameters: values.parameters
      });

      setExecuteModalVisible(false);
      executeForm.resetFields();
      message.success('脚本执行已启动');

      // 重新加载执行记录
      setTimeout(() => {
        loadExecutions();
      }, 1000);

    } catch (error) {
      console.error('脚本执行失败:', error);
      message.error('脚本执行失败');
    }
  };

  return (
    <div>
      <Tabs defaultActiveKey="scripts">
        <TabPane tab="脚本管理" key="scripts">
          <Card
            title="脚本列表"
            extra={
              <Space>
                <Button icon={<UploadOutlined />}>
                  导入脚本
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddScript}>
                  新建脚本
                </Button>
              </Space>
            }
          >
            <Table
              columns={scriptColumns}
              dataSource={scripts}
              rowKey="id"
              loading={loading}
            />
          </Card>
        </TabPane>

        <TabPane tab="执行记录" key="executions">
          <Card title="执行记录">
            <Table
              columns={executionColumns}
              dataSource={executions}
              rowKey="id"
              loading={loading}
            />
          </Card>
        </TabPane>
      </Tabs>

      <Modal
        title={editingScript ? '编辑脚本' : '新建脚本'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            script_type: 'shell',
            category: '系统管理',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="脚本名称"
                name="name"
                rules={[{ required: true, message: '请输入脚本名称' }]}
              >
                <Input placeholder="请输入脚本名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="脚本类型"
                name="script_type"
                rules={[{ required: true, message: '请选择脚本类型' }]}
              >
                <Select>
                  <Option value="shell">Shell</Option>
                  <Option value="python">Python</Option>
                  <Option value="javascript">JavaScript</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="分类"
                name="category"
              >
                <Input placeholder="请输入分类" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="标签"
                name="tags"
              >
                <Select mode="tags" placeholder="请输入标签">
                  <Option value="system">system</Option>
                  <Option value="docker">docker</Option>
                  <Option value="monitoring">monitoring</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="描述"
            name="description"
            rules={[{ required: true, message: '请输入脚本描述' }]}
          >
            <TextArea rows={3} placeholder="请输入脚本描述" />
          </Form.Item>

          <Form.Item
            label="脚本内容"
            name="content"
            rules={[{ required: true, message: '请输入脚本内容' }]}
          >
            <TextArea 
              rows={10} 
              placeholder="请输入脚本内容"
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={`执行脚本 - ${selectedScript?.name}`}
        open={executeModalVisible}
        onOk={handleExecuteOk}
        onCancel={() => setExecuteModalVisible(false)}
      >
        <Form
          form={executeForm}
          layout="vertical"
        >
          <Form.Item
            label="目标服务器"
            name="server"
            rules={[{ required: true, message: '请选择目标服务器' }]}
          >
            <Select placeholder="请选择服务器">
              <Option value="测试服务器1">测试服务器1</Option>
              <Option value="生产服务器1">生产服务器1</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="执行参数"
            name="parameters"
          >
            <TextArea rows={3} placeholder="请输入执行参数（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ScriptManagement;
