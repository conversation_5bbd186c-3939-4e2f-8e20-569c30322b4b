# 设备管理系统部署指南

## 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 20GB以上可用空间
- 网络: 稳定的网络连接

### 软件要求
- Docker 20.10+
- Docker Compose 2.0+
- Git

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd device-management-system
```

### 2. 环境配置
创建环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_DATABASE=device_management
MYSQL_USER=app_user
MYSQL_PASSWORD=your_app_password

# 应用配置
SECRET_KEY=your_secret_key_here
DEBUG=false

# Redis配置
REDIS_PASSWORD=your_redis_password
```

### 3. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 初始化数据库
```bash
# 执行数据库迁移
docker-compose exec backend python -m alembic upgrade head

# 创建初始管理员用户（可选）
docker-compose exec backend python scripts/create_admin.py
```

### 5. 访问系统
- Web界面: http://localhost:3000
- API文档: http://localhost:8000/docs
- 管理后台: http://localhost:3000/admin

## 开发环境部署

### 前端开发
```bash
cd frontend
npm install
npm start
```

### 后端开发
```bash
cd backend
pip install -r requirements.txt
python simple_main.py
```

## 生产环境部署

### 1. 安全配置
- 修改默认密码
- 配置SSL证书
- 设置防火墙规则
- 启用日志审计

### 2. 性能优化
- 配置数据库连接池
- 启用Redis缓存
- 配置负载均衡
- 设置监控告警

### 3. 备份策略
```bash
# 数据库备份
docker-compose exec mysql mysqldump -u root -p device_management > backup.sql

# 恢复数据库
docker-compose exec -i mysql mysql -u root -p device_management < backup.sql
```

## 监控和维护

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查数据库连接
docker-compose exec backend python -c "from app.database import engine; print(engine.execute('SELECT 1').scalar())"
```

### 日志管理
```bash
# 查看应用日志
docker-compose logs backend

# 查看访问日志
docker-compose logs nginx

# 清理日志
docker-compose exec backend find /app/logs -name "*.log" -mtime +7 -delete
```

### 性能监控
- CPU使用率
- 内存使用率
- 磁盘空间
- 网络流量
- 数据库连接数
- API响应时间

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口占用: `netstat -tulpn | grep :8000`
   - 检查Docker状态: `docker-compose ps`
   - 查看错误日志: `docker-compose logs`

2. **数据库连接失败**
   - 检查数据库服务: `docker-compose exec mysql mysql -u root -p`
   - 验证连接配置: 检查环境变量
   - 重启数据库服务: `docker-compose restart mysql`

3. **前端页面无法加载**
   - 检查nginx配置
   - 验证API连接
   - 清除浏览器缓存

4. **SSH连接失败**
   - 验证目标服务器网络连通性
   - 检查SSH密钥和密码
   - 确认防火墙设置

### 调试命令
```bash
# 进入容器调试
docker-compose exec backend bash
docker-compose exec frontend sh

# 查看容器资源使用
docker stats

# 重启特定服务
docker-compose restart backend

# 重新构建服务
docker-compose build --no-cache backend
```

## 升级指南

### 1. 备份数据
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p device_management > backup_$(date +%Y%m%d).sql

# 备份配置文件
cp -r config config_backup_$(date +%Y%m%d)
```

### 2. 更新代码
```bash
git pull origin main
```

### 3. 更新服务
```bash
# 停止服务
docker-compose down

# 重新构建
docker-compose build

# 启动服务
docker-compose up -d

# 执行数据库迁移
docker-compose exec backend python -m alembic upgrade head
```

## 安全建议

1. **网络安全**
   - 使用HTTPS
   - 配置防火墙
   - 限制SSH访问
   - 使用VPN

2. **认证安全**
   - 强密码策略
   - 双因素认证
   - 定期更换密钥
   - 权限最小化

3. **数据安全**
   - 数据加密
   - 定期备份
   - 访问审计
   - 敏感数据脱敏

4. **系统安全**
   - 及时更新补丁
   - 安全扫描
   - 入侵检测
   - 日志监控

## 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查GitHub Issues
3. 联系技术支持团队

---

更多详细信息请参考项目文档和API文档。
